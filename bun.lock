{
  "lockfileVersion": 1,
  "workspaces": {
    "": {
      "name": "valon",
      "dependencies": {
        "@astrojs/check": "^0.9.4",
        "@astrojs/mdx": "^4.0.8",
        "@astrojs/rss": "^4.0.11",
        "@astrojs/sitemap": "^3.2.1",
        "@astrojs/tailwind": "^6.0.0",
        "@fontsource/geist-mono": "^5.1.0",
        "@fontsource/geist-sans": "^5.1.0",
        "astro": "^5.2.5",
        "astro-pagefind": "^1.6.0",
        "clsx": "^2.1.1",
        "tailwind-merge": "^2.5.2",
        "tailwindcss": "^3.4.12",
        "typescript": "^5.6.2",
      },
      "devDependencies": {
        "@tailwindcss/typography": "^0.5.15",
        "pagefind": "^1.1.1",
        "prettier": "^3.3.3",
        "prettier-plugin-astro": "^0.14.1",
        "prettier-plugin-tailwindcss": "^0.6.6",
      },
    },
  },
  "packages": {
    "@alloc/quick-lru": [
      "@alloc/quick-lru@5.2.0",
      "",
      {},
      "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==",
    ],

    "@astrojs/check": [
      "@astrojs/check@0.9.4",
      "",
      {
        "dependencies": {
          "@astrojs/language-server": "^2.15.0",
          "chokidar": "^4.0.1",
          "kleur": "^4.1.5",
          "yargs": "^17.7.2",
        },
        "peerDependencies": { "typescript": "^5.0.0" },
        "bin": { "astro-check": "dist/bin.js" },
      },
      "sha512-IOheHwCtpUfvogHHsvu0AbeRZEnjJg3MopdLddkJE70mULItS/Vh37BHcI00mcOJcH1vhD3odbpvWokpxam7xA==",
    ],

    "@astrojs/compiler": [
      "@astrojs/compiler@2.10.3",
      "",
      {},
      "sha512-bL/O7YBxsFt55YHU021oL+xz+B/9HvGNId3F9xURN16aeqDK9juHGktdkCSXz+U4nqFACq6ZFvWomOzhV+zfPw==",
    ],

    "@astrojs/internal-helpers": [
      "@astrojs/internal-helpers@0.5.1",
      "",
      {},
      "sha512-M7rAge1n2+aOSxNvKUFa0u/KFn0W+sZy7EW91KOSERotm2Ti8qs+1K0xx3zbOxtAVrmJb5/J98eohVvvEqtNkw==",
    ],

    "@astrojs/language-server": [
      "@astrojs/language-server@2.15.4",
      "",
      {
        "dependencies": {
          "@astrojs/compiler": "^2.10.3",
          "@astrojs/yaml2ts": "^0.2.2",
          "@jridgewell/sourcemap-codec": "^1.4.15",
          "@volar/kit": "~2.4.7",
          "@volar/language-core": "~2.4.7",
          "@volar/language-server": "~2.4.7",
          "@volar/language-service": "~2.4.7",
          "fast-glob": "^3.2.12",
          "muggle-string": "^0.4.1",
          "volar-service-css": "0.0.62",
          "volar-service-emmet": "0.0.62",
          "volar-service-html": "0.0.62",
          "volar-service-prettier": "0.0.62",
          "volar-service-typescript": "0.0.62",
          "volar-service-typescript-twoslash-queries": "0.0.62",
          "volar-service-yaml": "0.0.62",
          "vscode-html-languageservice": "^5.2.0",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": {
          "prettier": "^3.0.0",
          "prettier-plugin-astro": ">=0.11.0",
        },
        "bin": { "astro-ls": "bin/nodeServer.js" },
      },
      "sha512-JivzASqTPR2bao9BWsSc/woPHH7OGSGc9aMxXL4U6egVTqBycB3ZHdBJPuOCVtcGLrzdWTosAqVPz1BVoxE0+A==",
    ],

    "@astrojs/markdown-remark": [
      "@astrojs/markdown-remark@6.1.0",
      "",
      {
        "dependencies": {
          "@astrojs/prism": "3.2.0",
          "github-slugger": "^2.0.0",
          "hast-util-from-html": "^2.0.3",
          "hast-util-to-text": "^4.0.2",
          "import-meta-resolve": "^4.1.0",
          "js-yaml": "^4.1.0",
          "mdast-util-definitions": "^6.0.0",
          "rehype-raw": "^7.0.0",
          "rehype-stringify": "^10.0.1",
          "remark-gfm": "^4.0.0",
          "remark-parse": "^11.0.0",
          "remark-rehype": "^11.1.1",
          "remark-smartypants": "^3.0.2",
          "shiki": "^1.29.1",
          "smol-toml": "^1.3.1",
          "unified": "^11.0.5",
          "unist-util-remove-position": "^5.0.0",
          "unist-util-visit": "^5.0.0",
          "unist-util-visit-parents": "^6.0.1",
          "vfile": "^6.0.3",
        },
      },
      "sha512-emZNNSTPGgPc3V399Cazpp5+snogjaF04ocOSQn9vy3Kw/eIC4vTQjXOrWDEoSEy+AwPDZX9bQ4wd3bxhpmGgQ==",
    ],

    "@astrojs/mdx": [
      "@astrojs/mdx@4.0.8",
      "",
      {
        "dependencies": {
          "@astrojs/markdown-remark": "6.1.0",
          "@mdx-js/mdx": "^3.1.0",
          "acorn": "^8.14.0",
          "es-module-lexer": "^1.6.0",
          "estree-util-visit": "^2.0.0",
          "hast-util-to-html": "^9.0.4",
          "kleur": "^4.1.5",
          "rehype-raw": "^7.0.0",
          "remark-gfm": "^4.0.0",
          "remark-smartypants": "^3.0.2",
          "source-map": "^0.7.4",
          "unist-util-visit": "^5.0.0",
          "vfile": "^6.0.3",
        },
        "peerDependencies": { "astro": "^5.0.0" },
      },
      "sha512-/aiLr2yQ55W9AbpyOgfMtFXk7g2t7XoWdC2Avps/NqxAx4aYONDLneX43D79QwgqdjFhin7o3cIPp/vVppMbaA==",
    ],

    "@astrojs/prism": [
      "@astrojs/prism@3.2.0",
      "",
      { "dependencies": { "prismjs": "^1.29.0" } },
      "sha512-GilTHKGCW6HMq7y3BUv9Ac7GMe/MO9gi9GW62GzKtth0SwukCu/qp2wLiGpEujhY+VVhaG9v7kv/5vFzvf4NYw==",
    ],

    "@astrojs/rss": [
      "@astrojs/rss@4.0.11",
      "",
      { "dependencies": { "fast-xml-parser": "^4.5.0", "kleur": "^4.1.5" } },
      "sha512-3e3H8i6kc97KGnn9iaZBJpIkdoQi8MmR5zH5R+dWsfCM44lLTszOqy1OBfGGxDt56mpQkYVtZJWoxMyWuUZBfw==",
    ],

    "@astrojs/sitemap": [
      "@astrojs/sitemap@3.2.1",
      "",
      {
        "dependencies": {
          "sitemap": "^8.0.0",
          "stream-replace-string": "^2.0.0",
          "zod": "^3.23.8",
        },
      },
      "sha512-uxMfO8f7pALq0ADL6Lk68UV6dNYjJ2xGUzyjjVj60JLBs5a6smtlkBYv3tQ0DzoqwS7c9n4FUx5lgv0yPo/fgA==",
    ],

    "@astrojs/tailwind": [
      "@astrojs/tailwind@6.0.0",
      "",
      {
        "dependencies": {
          "autoprefixer": "^10.4.20",
          "postcss": "^8.5.1",
          "postcss-load-config": "^4.0.2",
        },
        "peerDependencies": {
          "astro": "^3.0.0 || ^4.0.0 || ^5.0.0",
          "tailwindcss": "^3.0.24",
        },
      },
      "sha512-GbEK2/h0nvY8i6g1GZT5ddHAgj71lQIf34/j7+jPLhUti3xDxKcWElApDe2jWvqAaKl2oKEh045ITSm8mU9BVQ==",
    ],

    "@astrojs/telemetry": [
      "@astrojs/telemetry@3.2.0",
      "",
      {
        "dependencies": {
          "ci-info": "^4.1.0",
          "debug": "^4.3.7",
          "dlv": "^1.1.3",
          "dset": "^3.1.4",
          "is-docker": "^3.0.0",
          "is-wsl": "^3.1.0",
          "which-pm-runs": "^1.1.0",
        },
      },
      "sha512-wxhSKRfKugLwLlr4OFfcqovk+LIFtKwLyGPqMsv+9/ibqqnW3Gv7tBhtKEb0gAyUAC4G9BTVQeQahqnQAhd6IQ==",
    ],

    "@astrojs/yaml2ts": [
      "@astrojs/yaml2ts@0.2.2",
      "",
      { "dependencies": { "yaml": "^2.5.0" } },
      "sha512-GOfvSr5Nqy2z5XiwqTouBBpy5FyI6DEe+/g/Mk5am9SjILN1S5fOEvYK0GuWHg98yS/dobP4m8qyqw/URW35fQ==",
    ],

    "@babel/helper-string-parser": [
      "@babel/helper-string-parser@7.25.9",
      "",
      {},
      "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==",
    ],

    "@babel/helper-validator-identifier": [
      "@babel/helper-validator-identifier@7.25.9",
      "",
      {},
      "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==",
    ],

    "@babel/parser": [
      "@babel/parser@7.26.3",
      "",
      {
        "dependencies": { "@babel/types": "^7.26.3" },
        "bin": { "parser": "bin/babel-parser.js" },
      },
      "sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA==",
    ],

    "@babel/types": [
      "@babel/types@7.26.3",
      "",
      {
        "dependencies": {
          "@babel/helper-string-parser": "^7.25.9",
          "@babel/helper-validator-identifier": "^7.25.9",
        },
      },
      "sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA==",
    ],

    "@emmetio/abbreviation": [
      "@emmetio/abbreviation@2.3.3",
      "",
      { "dependencies": { "@emmetio/scanner": "^1.0.4" } },
      "sha512-mgv58UrU3rh4YgbE/TzgLQwJ3pFsHHhCLqY20aJq+9comytTXUDNGG/SMtSeMJdkpxgXSXunBGLD8Boka3JyVA==",
    ],

    "@emmetio/css-abbreviation": [
      "@emmetio/css-abbreviation@2.1.8",
      "",
      { "dependencies": { "@emmetio/scanner": "^1.0.4" } },
      "sha512-s9yjhJ6saOO/uk1V74eifykk2CBYi01STTK3WlXWGOepyKa23ymJ053+DNQjpFcy1ingpaO7AxCcwLvHFY9tuw==",
    ],

    "@emmetio/css-parser": [
      "@emmetio/css-parser@0.4.0",
      "",
      {
        "dependencies": {
          "@emmetio/stream-reader": "^2.2.0",
          "@emmetio/stream-reader-utils": "^0.1.0",
        },
      },
      "sha512-z7wkxRSZgrQHXVzObGkXG+Vmj3uRlpM11oCZ9pbaz0nFejvCDmAiNDpY75+wgXOcffKpj4rzGtwGaZxfJKsJxw==",
    ],

    "@emmetio/html-matcher": [
      "@emmetio/html-matcher@1.3.0",
      "",
      { "dependencies": { "@emmetio/scanner": "^1.0.0" } },
      "sha512-NTbsvppE5eVyBMuyGfVu2CRrLvo7J4YHb6t9sBFLyY03WYhXET37qA4zOYUjBWFCRHO7pS1B9khERtY0f5JXPQ==",
    ],

    "@emmetio/scanner": [
      "@emmetio/scanner@1.0.4",
      "",
      {},
      "sha512-IqRuJtQff7YHHBk4G8YZ45uB9BaAGcwQeVzgj/zj8/UdOhtQpEIupUhSk8dys6spFIWVZVeK20CzGEnqR5SbqA==",
    ],

    "@emmetio/stream-reader": [
      "@emmetio/stream-reader@2.2.0",
      "",
      {},
      "sha512-fXVXEyFA5Yv3M3n8sUGT7+fvecGrZP4k6FnWWMSZVQf69kAq0LLpaBQLGcPR30m3zMmKYhECP4k/ZkzvhEW5kw==",
    ],

    "@emmetio/stream-reader-utils": [
      "@emmetio/stream-reader-utils@0.1.0",
      "",
      {},
      "sha512-ZsZ2I9Vzso3Ho/pjZFsmmZ++FWeEd/txqybHTm4OgaZzdS8V9V/YYWQwg5TC38Z7uLWUV1vavpLLbjJtKubR1A==",
    ],

    "@emnapi/runtime": [
      "@emnapi/runtime@1.3.1",
      "",
      { "dependencies": { "tslib": "^2.4.0" } },
      "sha512-kEBmG8KyqtxJZv+ygbEim+KCGtIq1fC22Ms3S4ziXmYKm8uyoLX0MHONVKwp+9opg390VaKRNt4a7A9NwmpNhw==",
    ],

    "@esbuild/aix-ppc64": [
      "@esbuild/aix-ppc64@0.24.2",
      "",
      { "os": "aix", "cpu": "ppc64" },
      "sha512-thpVCb/rhxE/BnMLQ7GReQLLN8q9qbHmI55F4489/ByVg2aQaQ6kbcLb6FHkocZzQhxc4gx0sCk0tJkKBFzDhA==",
    ],

    "@esbuild/android-arm": [
      "@esbuild/android-arm@0.24.2",
      "",
      { "os": "android", "cpu": "arm" },
      "sha512-tmwl4hJkCfNHwFB3nBa8z1Uy3ypZpxqxfTQOcHX+xRByyYgunVbZ9MzUUfb0RxaHIMnbHagwAxuTL+tnNM+1/Q==",
    ],

    "@esbuild/android-arm64": [
      "@esbuild/android-arm64@0.24.2",
      "",
      { "os": "android", "cpu": "arm64" },
      "sha512-cNLgeqCqV8WxfcTIOeL4OAtSmL8JjcN6m09XIgro1Wi7cF4t/THaWEa7eL5CMoMBdjoHOTh/vwTO/o2TRXIyzg==",
    ],

    "@esbuild/android-x64": [
      "@esbuild/android-x64@0.24.2",
      "",
      { "os": "android", "cpu": "x64" },
      "sha512-B6Q0YQDqMx9D7rvIcsXfmJfvUYLoP722bgfBlO5cGvNVb5V/+Y7nhBE3mHV9OpxBf4eAS2S68KZztiPaWq4XYw==",
    ],

    "@esbuild/darwin-arm64": [
      "@esbuild/darwin-arm64@0.24.2",
      "",
      { "os": "darwin", "cpu": "arm64" },
      "sha512-kj3AnYWc+CekmZnS5IPu9D+HWtUI49hbnyqk0FLEJDbzCIQt7hg7ucF1SQAilhtYpIujfaHr6O0UHlzzSPdOeA==",
    ],

    "@esbuild/darwin-x64": [
      "@esbuild/darwin-x64@0.24.2",
      "",
      { "os": "darwin", "cpu": "x64" },
      "sha512-WeSrmwwHaPkNR5H3yYfowhZcbriGqooyu3zI/3GGpF8AyUdsrrP0X6KumITGA9WOyiJavnGZUwPGvxvwfWPHIA==",
    ],

    "@esbuild/freebsd-arm64": [
      "@esbuild/freebsd-arm64@0.24.2",
      "",
      { "os": "freebsd", "cpu": "arm64" },
      "sha512-UN8HXjtJ0k/Mj6a9+5u6+2eZ2ERD7Edt1Q9IZiB5UZAIdPnVKDoG7mdTVGhHJIeEml60JteamR3qhsr1r8gXvg==",
    ],

    "@esbuild/freebsd-x64": [
      "@esbuild/freebsd-x64@0.24.2",
      "",
      { "os": "freebsd", "cpu": "x64" },
      "sha512-TvW7wE/89PYW+IevEJXZ5sF6gJRDY/14hyIGFXdIucxCsbRmLUcjseQu1SyTko+2idmCw94TgyaEZi9HUSOe3Q==",
    ],

    "@esbuild/linux-arm": [
      "@esbuild/linux-arm@0.24.2",
      "",
      { "os": "linux", "cpu": "arm" },
      "sha512-n0WRM/gWIdU29J57hJyUdIsk0WarGd6To0s+Y+LwvlC55wt+GT/OgkwoXCXvIue1i1sSNWblHEig00GBWiJgfA==",
    ],

    "@esbuild/linux-arm64": [
      "@esbuild/linux-arm64@0.24.2",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-7HnAD6074BW43YvvUmE/35Id9/NB7BeX5EoNkK9obndmZBUk8xmJJeU7DwmUeN7tkysslb2eSl6CTrYz6oEMQg==",
    ],

    "@esbuild/linux-ia32": [
      "@esbuild/linux-ia32@0.24.2",
      "",
      { "os": "linux", "cpu": "ia32" },
      "sha512-sfv0tGPQhcZOgTKO3oBE9xpHuUqguHvSo4jl+wjnKwFpapx+vUDcawbwPNuBIAYdRAvIDBfZVvXprIj3HA+Ugw==",
    ],

    "@esbuild/linux-loong64": [
      "@esbuild/linux-loong64@0.24.2",
      "",
      { "os": "linux", "cpu": "none" },
      "sha512-CN9AZr8kEndGooS35ntToZLTQLHEjtVB5n7dl8ZcTZMonJ7CCfStrYhrzF97eAecqVbVJ7APOEe18RPI4KLhwQ==",
    ],

    "@esbuild/linux-mips64el": [
      "@esbuild/linux-mips64el@0.24.2",
      "",
      { "os": "linux", "cpu": "none" },
      "sha512-iMkk7qr/wl3exJATwkISxI7kTcmHKE+BlymIAbHO8xanq/TjHaaVThFF6ipWzPHryoFsesNQJPE/3wFJw4+huw==",
    ],

    "@esbuild/linux-ppc64": [
      "@esbuild/linux-ppc64@0.24.2",
      "",
      { "os": "linux", "cpu": "ppc64" },
      "sha512-shsVrgCZ57Vr2L8mm39kO5PPIb+843FStGt7sGGoqiiWYconSxwTiuswC1VJZLCjNiMLAMh34jg4VSEQb+iEbw==",
    ],

    "@esbuild/linux-riscv64": [
      "@esbuild/linux-riscv64@0.24.2",
      "",
      { "os": "linux", "cpu": "none" },
      "sha512-4eSFWnU9Hhd68fW16GD0TINewo1L6dRrB+oLNNbYyMUAeOD2yCK5KXGK1GH4qD/kT+bTEXjsyTCiJGHPZ3eM9Q==",
    ],

    "@esbuild/linux-s390x": [
      "@esbuild/linux-s390x@0.24.2",
      "",
      { "os": "linux", "cpu": "s390x" },
      "sha512-S0Bh0A53b0YHL2XEXC20bHLuGMOhFDO6GN4b3YjRLK//Ep3ql3erpNcPlEFed93hsQAjAQDNsvcK+hV90FubSw==",
    ],

    "@esbuild/linux-x64": [
      "@esbuild/linux-x64@0.24.2",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-8Qi4nQcCTbLnK9WoMjdC9NiTG6/E38RNICU6sUNqK0QFxCYgoARqVqxdFmWkdonVsvGqWhmm7MO0jyTqLqwj0Q==",
    ],

    "@esbuild/netbsd-arm64": [
      "@esbuild/netbsd-arm64@0.24.2",
      "",
      { "os": "none", "cpu": "arm64" },
      "sha512-wuLK/VztRRpMt9zyHSazyCVdCXlpHkKm34WUyinD2lzK07FAHTq0KQvZZlXikNWkDGoT6x3TD51jKQ7gMVpopw==",
    ],

    "@esbuild/netbsd-x64": [
      "@esbuild/netbsd-x64@0.24.2",
      "",
      { "os": "none", "cpu": "x64" },
      "sha512-VefFaQUc4FMmJuAxmIHgUmfNiLXY438XrL4GDNV1Y1H/RW3qow68xTwjZKfj/+Plp9NANmzbH5R40Meudu8mmw==",
    ],

    "@esbuild/openbsd-arm64": [
      "@esbuild/openbsd-arm64@0.24.2",
      "",
      { "os": "openbsd", "cpu": "arm64" },
      "sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==",
    ],

    "@esbuild/openbsd-x64": [
      "@esbuild/openbsd-x64@0.24.2",
      "",
      { "os": "openbsd", "cpu": "x64" },
      "sha512-+iDS6zpNM6EnJyWv0bMGLWSWeXGN/HTaF/LXHXHwejGsVi+ooqDfMCCTerNFxEkM3wYVcExkeGXNqshc9iMaOA==",
    ],

    "@esbuild/sunos-x64": [
      "@esbuild/sunos-x64@0.24.2",
      "",
      { "os": "sunos", "cpu": "x64" },
      "sha512-hTdsW27jcktEvpwNHJU4ZwWFGkz2zRJUz8pvddmXPtXDzVKTTINmlmga3ZzwcuMpUvLw7JkLy9QLKyGpD2Yxig==",
    ],

    "@esbuild/win32-arm64": [
      "@esbuild/win32-arm64@0.24.2",
      "",
      { "os": "win32", "cpu": "arm64" },
      "sha512-LihEQ2BBKVFLOC9ZItT9iFprsE9tqjDjnbulhHoFxYQtQfai7qfluVODIYxt1PgdoyQkz23+01rzwNwYfutxUQ==",
    ],

    "@esbuild/win32-ia32": [
      "@esbuild/win32-ia32@0.24.2",
      "",
      { "os": "win32", "cpu": "ia32" },
      "sha512-q+iGUwfs8tncmFC9pcnD5IvRHAzmbwQ3GPS5/ceCyHdjXubwQWI12MKWSNSMYLJMq23/IUCvJMS76PDqXe1fxA==",
    ],

    "@esbuild/win32-x64": [
      "@esbuild/win32-x64@0.24.2",
      "",
      { "os": "win32", "cpu": "x64" },
      "sha512-7VTgWzgMGvup6aSqDPLiW5zHaxYJGTO4OokMjIlrCtf+VpEL+cXKtCvg723iguPYI5oaUNdS+/V7OU2gvXVWEg==",
    ],

    "@fontsource/geist-mono": [
      "@fontsource/geist-mono@5.1.1",
      "",
      {},
      "sha512-Rr/VXX3qyRsLgEFwFUqhDS7PORzQUBDLoQc8bONbBz+hZ+SD8U0nEzHqRFBzT3zFcsf/0bMyCQPZUktKwPt3GQ==",
    ],

    "@fontsource/geist-sans": [
      "@fontsource/geist-sans@5.1.0",
      "",
      {},
      "sha512-4AI/UxIcZiYgK6/cTo4R9oM4VHHLcFdHVv7m2+M42VyY16hhKJADrGe4HPjTxvolmce2IZX/jc6sFJ+GkLB64w==",
    ],

    "@img/sharp-darwin-arm64": [
      "@img/sharp-darwin-arm64@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-darwin-arm64": "1.0.4" },
        "os": "darwin",
        "cpu": "arm64",
      },
      "sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==",
    ],

    "@img/sharp-darwin-x64": [
      "@img/sharp-darwin-x64@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-darwin-x64": "1.0.4" },
        "os": "darwin",
        "cpu": "x64",
      },
      "sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==",
    ],

    "@img/sharp-libvips-darwin-arm64": [
      "@img/sharp-libvips-darwin-arm64@1.0.4",
      "",
      { "os": "darwin", "cpu": "arm64" },
      "sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==",
    ],

    "@img/sharp-libvips-darwin-x64": [
      "@img/sharp-libvips-darwin-x64@1.0.4",
      "",
      { "os": "darwin", "cpu": "x64" },
      "sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==",
    ],

    "@img/sharp-libvips-linux-arm": [
      "@img/sharp-libvips-linux-arm@1.0.5",
      "",
      { "os": "linux", "cpu": "arm" },
      "sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==",
    ],

    "@img/sharp-libvips-linux-arm64": [
      "@img/sharp-libvips-linux-arm64@1.0.4",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==",
    ],

    "@img/sharp-libvips-linux-s390x": [
      "@img/sharp-libvips-linux-s390x@1.0.4",
      "",
      { "os": "linux", "cpu": "s390x" },
      "sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==",
    ],

    "@img/sharp-libvips-linux-x64": [
      "@img/sharp-libvips-linux-x64@1.0.4",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==",
    ],

    "@img/sharp-libvips-linuxmusl-arm64": [
      "@img/sharp-libvips-linuxmusl-arm64@1.0.4",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==",
    ],

    "@img/sharp-libvips-linuxmusl-x64": [
      "@img/sharp-libvips-linuxmusl-x64@1.0.4",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==",
    ],

    "@img/sharp-linux-arm": [
      "@img/sharp-linux-arm@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-linux-arm": "1.0.5" },
        "os": "linux",
        "cpu": "arm",
      },
      "sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==",
    ],

    "@img/sharp-linux-arm64": [
      "@img/sharp-linux-arm64@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-linux-arm64": "1.0.4" },
        "os": "linux",
        "cpu": "arm64",
      },
      "sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==",
    ],

    "@img/sharp-linux-s390x": [
      "@img/sharp-linux-s390x@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-linux-s390x": "1.0.4" },
        "os": "linux",
        "cpu": "s390x",
      },
      "sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==",
    ],

    "@img/sharp-linux-x64": [
      "@img/sharp-linux-x64@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-linux-x64": "1.0.4" },
        "os": "linux",
        "cpu": "x64",
      },
      "sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==",
    ],

    "@img/sharp-linuxmusl-arm64": [
      "@img/sharp-linuxmusl-arm64@0.33.5",
      "",
      {
        "optionalDependencies": {
          "@img/sharp-libvips-linuxmusl-arm64": "1.0.4",
        },
        "os": "linux",
        "cpu": "arm64",
      },
      "sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==",
    ],

    "@img/sharp-linuxmusl-x64": [
      "@img/sharp-linuxmusl-x64@0.33.5",
      "",
      {
        "optionalDependencies": { "@img/sharp-libvips-linuxmusl-x64": "1.0.4" },
        "os": "linux",
        "cpu": "x64",
      },
      "sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==",
    ],

    "@img/sharp-wasm32": [
      "@img/sharp-wasm32@0.33.5",
      "",
      { "dependencies": { "@emnapi/runtime": "^1.2.0" }, "cpu": "none" },
      "sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==",
    ],

    "@img/sharp-win32-ia32": [
      "@img/sharp-win32-ia32@0.33.5",
      "",
      { "os": "win32", "cpu": "ia32" },
      "sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==",
    ],

    "@img/sharp-win32-x64": [
      "@img/sharp-win32-x64@0.33.5",
      "",
      { "os": "win32", "cpu": "x64" },
      "sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==",
    ],

    "@isaacs/cliui": [
      "@isaacs/cliui@8.0.2",
      "",
      {
        "dependencies": {
          "string-width": "^5.1.2",
          "string-width-cjs": "npm:string-width@^4.2.0",
          "strip-ansi": "^7.0.1",
          "strip-ansi-cjs": "npm:strip-ansi@^6.0.1",
          "wrap-ansi": "^8.1.0",
          "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0",
        },
      },
      "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==",
    ],

    "@jridgewell/gen-mapping": [
      "@jridgewell/gen-mapping@0.3.8",
      "",
      {
        "dependencies": {
          "@jridgewell/set-array": "^1.2.1",
          "@jridgewell/sourcemap-codec": "^1.4.10",
          "@jridgewell/trace-mapping": "^0.3.24",
        },
      },
      "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==",
    ],

    "@jridgewell/resolve-uri": [
      "@jridgewell/resolve-uri@3.1.2",
      "",
      {},
      "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",
    ],

    "@jridgewell/set-array": [
      "@jridgewell/set-array@1.2.1",
      "",
      {},
      "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
    ],

    "@jridgewell/sourcemap-codec": [
      "@jridgewell/sourcemap-codec@1.5.0",
      "",
      {},
      "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==",
    ],

    "@jridgewell/trace-mapping": [
      "@jridgewell/trace-mapping@0.3.25",
      "",
      {
        "dependencies": {
          "@jridgewell/resolve-uri": "^3.1.0",
          "@jridgewell/sourcemap-codec": "^1.4.14",
        },
      },
      "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
    ],

    "@mdx-js/mdx": [
      "@mdx-js/mdx@3.1.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "@types/estree-jsx": "^1.0.0",
          "@types/hast": "^3.0.0",
          "@types/mdx": "^2.0.0",
          "collapse-white-space": "^2.0.0",
          "devlop": "^1.0.0",
          "estree-util-is-identifier-name": "^3.0.0",
          "estree-util-scope": "^1.0.0",
          "estree-walker": "^3.0.0",
          "hast-util-to-jsx-runtime": "^2.0.0",
          "markdown-extensions": "^2.0.0",
          "recma-build-jsx": "^1.0.0",
          "recma-jsx": "^1.0.0",
          "recma-stringify": "^1.0.0",
          "rehype-recma": "^1.0.0",
          "remark-mdx": "^3.0.0",
          "remark-parse": "^11.0.0",
          "remark-rehype": "^11.0.0",
          "source-map": "^0.7.0",
          "unified": "^11.0.0",
          "unist-util-position-from-estree": "^2.0.0",
          "unist-util-stringify-position": "^4.0.0",
          "unist-util-visit": "^5.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-/QxEhPAvGwbQmy1Px8F899L5Uc2KZ6JtXwlCgJmjSTBedwOZkByYcBG4GceIGPXRDsmfxhHazuS+hlOShRLeDw==",
    ],

    "@nodelib/fs.scandir": [
      "@nodelib/fs.scandir@2.1.5",
      "",
      {
        "dependencies": {
          "@nodelib/fs.stat": "2.0.5",
          "run-parallel": "^1.1.9",
        },
      },
      "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",
    ],

    "@nodelib/fs.stat": [
      "@nodelib/fs.stat@2.0.5",
      "",
      {},
      "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",
    ],

    "@nodelib/fs.walk": [
      "@nodelib/fs.walk@1.2.8",
      "",
      { "dependencies": { "@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0" } },
      "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",
    ],

    "@oslojs/encoding": [
      "@oslojs/encoding@1.1.0",
      "",
      {},
      "sha512-70wQhgYmndg4GCPxPPxPGevRKqTIJ2Nh4OkiMWmDAVYsTQ+Ta7Sq+rPevXyXGdzr30/qZBnyOalCszoMxlyldQ==",
    ],

    "@pagefind/darwin-arm64": [
      "@pagefind/darwin-arm64@1.3.0",
      "",
      { "os": "darwin", "cpu": "arm64" },
      "sha512-365BEGl6ChOsauRjyVpBjXybflXAOvoMROw3TucAROHIcdBvXk9/2AmEvGFU0r75+vdQI4LJdJdpH4Y6Yqaj4A==",
    ],

    "@pagefind/darwin-x64": [
      "@pagefind/darwin-x64@1.3.0",
      "",
      { "os": "darwin", "cpu": "x64" },
      "sha512-zlGHA23uuXmS8z3XxEGmbHpWDxXfPZ47QS06tGUq0HDcZjXjXHeLG+cboOy828QIV5FXsm9MjfkP5e4ZNbOkow==",
    ],

    "@pagefind/default-ui": [
      "@pagefind/default-ui@1.3.0",
      "",
      {},
      "sha512-CGKT9ccd3+oRK6STXGgfH+m0DbOKayX6QGlq38TfE1ZfUcPc5+ulTuzDbZUnMo+bubsEOIypm4Pl2iEyzZ1cNg==",
    ],

    "@pagefind/linux-arm64": [
      "@pagefind/linux-arm64@1.3.0",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-8lsxNAiBRUk72JvetSBXs4WRpYrQrVJXjlRRnOL6UCdBN9Nlsz0t7hWstRk36+JqHpGWOKYiuHLzGYqYAqoOnQ==",
    ],

    "@pagefind/linux-x64": [
      "@pagefind/linux-x64@1.3.0",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-hAvqdPJv7A20Ucb6FQGE6jhjqy+vZ6pf+s2tFMNtMBG+fzcdc91uTw7aP/1Vo5plD0dAOHwdxfkyw0ugal4kcQ==",
    ],

    "@pagefind/windows-x64": [
      "@pagefind/windows-x64@1.3.0",
      "",
      { "os": "win32", "cpu": "x64" },
      "sha512-BR1bIRWOMqkf8IoU576YDhij1Wd/Zf2kX/kCI0b2qzCKC8wcc2GQJaaRMCpzvCCrmliO4vtJ6RITp/AnoYUUmQ==",
    ],

    "@pkgjs/parseargs": [
      "@pkgjs/parseargs@0.11.0",
      "",
      {},
      "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==",
    ],

    "@polka/url": [
      "@polka/url@1.0.0-next.28",
      "",
      {},
      "sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==",
    ],

    "@rollup/pluginutils": [
      "@rollup/pluginutils@5.1.4",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "estree-walker": "^2.0.2",
          "picomatch": "^4.0.2",
        },
        "peerDependencies": { "rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0" },
      },
      "sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==",
    ],

    "@rollup/rollup-android-arm-eabi": [
      "@rollup/rollup-android-arm-eabi@4.34.6",
      "",
      { "os": "android", "cpu": "arm" },
      "sha512-+GcCXtOQoWuC7hhX1P00LqjjIiS/iOouHXhMdiDSnq/1DGTox4SpUvO52Xm+div6+106r+TcvOeo/cxvyEyTgg==",
    ],

    "@rollup/rollup-android-arm64": [
      "@rollup/rollup-android-arm64@4.34.6",
      "",
      { "os": "android", "cpu": "arm64" },
      "sha512-E8+2qCIjciYUnCa1AiVF1BkRgqIGW9KzJeesQqVfyRITGQN+dFuoivO0hnro1DjT74wXLRZ7QF8MIbz+luGaJA==",
    ],

    "@rollup/rollup-darwin-arm64": [
      "@rollup/rollup-darwin-arm64@4.34.6",
      "",
      { "os": "darwin", "cpu": "arm64" },
      "sha512-z9Ib+OzqN3DZEjX7PDQMHEhtF+t6Mi2z/ueChQPLS/qUMKY7Ybn5A2ggFoKRNRh1q1T03YTQfBTQCJZiepESAg==",
    ],

    "@rollup/rollup-darwin-x64": [
      "@rollup/rollup-darwin-x64@4.34.6",
      "",
      { "os": "darwin", "cpu": "x64" },
      "sha512-PShKVY4u0FDAR7jskyFIYVyHEPCPnIQY8s5OcXkdU8mz3Y7eXDJPdyM/ZWjkYdR2m0izD9HHWA8sGcXn+Qrsyg==",
    ],

    "@rollup/rollup-freebsd-arm64": [
      "@rollup/rollup-freebsd-arm64@4.34.6",
      "",
      { "os": "freebsd", "cpu": "arm64" },
      "sha512-YSwyOqlDAdKqs0iKuqvRHLN4SrD2TiswfoLfvYXseKbL47ht1grQpq46MSiQAx6rQEN8o8URtpXARCpqabqxGQ==",
    ],

    "@rollup/rollup-freebsd-x64": [
      "@rollup/rollup-freebsd-x64@4.34.6",
      "",
      { "os": "freebsd", "cpu": "x64" },
      "sha512-HEP4CgPAY1RxXwwL5sPFv6BBM3tVeLnshF03HMhJYCNc6kvSqBgTMmsEjb72RkZBAWIqiPUyF1JpEBv5XT9wKQ==",
    ],

    "@rollup/rollup-linux-arm-gnueabihf": [
      "@rollup/rollup-linux-arm-gnueabihf@4.34.6",
      "",
      { "os": "linux", "cpu": "arm" },
      "sha512-88fSzjC5xeH9S2Vg3rPgXJULkHcLYMkh8faix8DX4h4TIAL65ekwuQMA/g2CXq8W+NJC43V6fUpYZNjaX3+IIg==",
    ],

    "@rollup/rollup-linux-arm-musleabihf": [
      "@rollup/rollup-linux-arm-musleabihf@4.34.6",
      "",
      { "os": "linux", "cpu": "arm" },
      "sha512-wM4ztnutBqYFyvNeR7Av+reWI/enK9tDOTKNF+6Kk2Q96k9bwhDDOlnCUNRPvromlVXo04riSliMBs/Z7RteEg==",
    ],

    "@rollup/rollup-linux-arm64-gnu": [
      "@rollup/rollup-linux-arm64-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-9RyprECbRa9zEjXLtvvshhw4CMrRa3K+0wcp3KME0zmBe1ILmvcVHnypZ/aIDXpRyfhSYSuN4EPdCCj5Du8FIA==",
    ],

    "@rollup/rollup-linux-arm64-musl": [
      "@rollup/rollup-linux-arm64-musl@4.34.6",
      "",
      { "os": "linux", "cpu": "arm64" },
      "sha512-qTmklhCTyaJSB05S+iSovfo++EwnIEZxHkzv5dep4qoszUMX5Ca4WM4zAVUMbfdviLgCSQOu5oU8YoGk1s6M9Q==",
    ],

    "@rollup/rollup-linux-loongarch64-gnu": [
      "@rollup/rollup-linux-loongarch64-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "none" },
      "sha512-4Qmkaps9yqmpjY5pvpkfOerYgKNUGzQpFxV6rnS7c/JfYbDSU0y6WpbbredB5cCpLFGJEqYX40WUmxMkwhWCjw==",
    ],

    "@rollup/rollup-linux-powerpc64le-gnu": [
      "@rollup/rollup-linux-powerpc64le-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "ppc64" },
      "sha512-Zsrtux3PuaxuBTX/zHdLaFmcofWGzaWW1scwLU3ZbW/X+hSsFbz9wDIp6XvnT7pzYRl9MezWqEqKy7ssmDEnuQ==",
    ],

    "@rollup/rollup-linux-riscv64-gnu": [
      "@rollup/rollup-linux-riscv64-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "none" },
      "sha512-aK+Zp+CRM55iPrlyKiU3/zyhgzWBxLVrw2mwiQSYJRobCURb781+XstzvA8Gkjg/hbdQFuDw44aUOxVQFycrAg==",
    ],

    "@rollup/rollup-linux-s390x-gnu": [
      "@rollup/rollup-linux-s390x-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "s390x" },
      "sha512-WoKLVrY9ogmaYPXwTH326+ErlCIgMmsoRSx6bO+l68YgJnlOXhygDYSZe/qbUJCSiCiZAQ+tKm88NcWuUXqOzw==",
    ],

    "@rollup/rollup-linux-x64-gnu": [
      "@rollup/rollup-linux-x64-gnu@4.34.6",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-Sht4aFvmA4ToHd2vFzwMFaQCiYm2lDFho5rPcvPBT5pCdC+GwHG6CMch4GQfmWTQ1SwRKS0dhDYb54khSrjDWw==",
    ],

    "@rollup/rollup-linux-x64-musl": [
      "@rollup/rollup-linux-x64-musl@4.34.6",
      "",
      { "os": "linux", "cpu": "x64" },
      "sha512-zmmpOQh8vXc2QITsnCiODCDGXFC8LMi64+/oPpPx5qz3pqv0s6x46ps4xoycfUiVZps5PFn1gksZzo4RGTKT+A==",
    ],

    "@rollup/rollup-win32-arm64-msvc": [
      "@rollup/rollup-win32-arm64-msvc@4.34.6",
      "",
      { "os": "win32", "cpu": "arm64" },
      "sha512-3/q1qUsO/tLqGBaD4uXsB6coVGB3usxw3qyeVb59aArCgedSF66MPdgRStUd7vbZOsko/CgVaY5fo2vkvPLWiA==",
    ],

    "@rollup/rollup-win32-ia32-msvc": [
      "@rollup/rollup-win32-ia32-msvc@4.34.6",
      "",
      { "os": "win32", "cpu": "ia32" },
      "sha512-oLHxuyywc6efdKVTxvc0135zPrRdtYVjtVD5GUm55I3ODxhU/PwkQFD97z16Xzxa1Fz0AEe4W/2hzRtd+IfpOA==",
    ],

    "@rollup/rollup-win32-x64-msvc": [
      "@rollup/rollup-win32-x64-msvc@4.34.6",
      "",
      { "os": "win32", "cpu": "x64" },
      "sha512-0PVwmgzZ8+TZ9oGBmdZoQVXflbvuwzN/HRclujpl4N/q3i+y0lqLw8n1bXA8ru3sApDjlmONaNAuYr38y1Kr9w==",
    ],

    "@shikijs/core": [
      "@shikijs/core@1.29.2",
      "",
      {
        "dependencies": {
          "@shikijs/engine-javascript": "1.29.2",
          "@shikijs/engine-oniguruma": "1.29.2",
          "@shikijs/types": "1.29.2",
          "@shikijs/vscode-textmate": "^10.0.1",
          "@types/hast": "^3.0.4",
          "hast-util-to-html": "^9.0.4",
        },
      },
      "sha512-vju0lY9r27jJfOY4Z7+Rt/nIOjzJpZ3y+nYpqtUZInVoXQ/TJZcfGnNOGnKjFdVZb8qexiCuSlZRKcGfhhTTZQ==",
    ],

    "@shikijs/engine-javascript": [
      "@shikijs/engine-javascript@1.29.2",
      "",
      {
        "dependencies": {
          "@shikijs/types": "1.29.2",
          "@shikijs/vscode-textmate": "^10.0.1",
          "oniguruma-to-es": "^2.2.0",
        },
      },
      "sha512-iNEZv4IrLYPv64Q6k7EPpOCE/nuvGiKl7zxdq0WFuRPF5PAE9PRo2JGq/d8crLusM59BRemJ4eOqrFrC4wiQ+A==",
    ],

    "@shikijs/engine-oniguruma": [
      "@shikijs/engine-oniguruma@1.29.2",
      "",
      {
        "dependencies": {
          "@shikijs/types": "1.29.2",
          "@shikijs/vscode-textmate": "^10.0.1",
        },
      },
      "sha512-7iiOx3SG8+g1MnlzZVDYiaeHe7Ez2Kf2HrJzdmGwkRisT7r4rak0e655AcM/tF9JG/kg5fMNYlLLKglbN7gBqA==",
    ],

    "@shikijs/langs": [
      "@shikijs/langs@1.29.2",
      "",
      { "dependencies": { "@shikijs/types": "1.29.2" } },
      "sha512-FIBA7N3LZ+223U7cJDUYd5shmciFQlYkFXlkKVaHsCPgfVLiO+e12FmQE6Tf9vuyEsFe3dIl8qGWKXgEHL9wmQ==",
    ],

    "@shikijs/themes": [
      "@shikijs/themes@1.29.2",
      "",
      { "dependencies": { "@shikijs/types": "1.29.2" } },
      "sha512-i9TNZlsq4uoyqSbluIcZkmPL9Bfi3djVxRnofUHwvx/h6SRW3cwgBC5SML7vsDcWyukY0eCzVN980rqP6qNl9g==",
    ],

    "@shikijs/types": [
      "@shikijs/types@1.29.2",
      "",
      {
        "dependencies": {
          "@shikijs/vscode-textmate": "^10.0.1",
          "@types/hast": "^3.0.4",
        },
      },
      "sha512-VJjK0eIijTZf0QSTODEXCqinjBn0joAHQ+aPSBzrv4O2d/QSbsMw+ZeSRx03kV34Hy7NzUvV/7NqfYGRLrASmw==",
    ],

    "@shikijs/vscode-textmate": [
      "@shikijs/vscode-textmate@10.0.1",
      "",
      {},
      "sha512-fTIQwLF+Qhuws31iw7Ncl1R3HUDtGwIipiJ9iU+UsDUwMhegFcQKQHd51nZjb7CArq0MvON8rbgCGQYWHUKAdg==",
    ],

    "@tailwindcss/typography": [
      "@tailwindcss/typography@0.5.15",
      "",
      {
        "dependencies": {
          "lodash.castarray": "^4.4.0",
          "lodash.isplainobject": "^4.0.6",
          "lodash.merge": "^4.6.2",
          "postcss-selector-parser": "6.0.10",
        },
        "peerDependencies": {
          "tailwindcss": ">=3.0.0 || insiders || >=4.0.0-alpha.20",
        },
      },
      "sha512-AqhlCXl+8grUz8uqExv5OTtgpjuVIwFTSXTrh8y9/pw6q2ek7fJ+Y8ZEVw7EB2DCcuCOtEjf9w3+J3rzts01uA==",
    ],

    "@types/acorn": [
      "@types/acorn@4.0.6",
      "",
      { "dependencies": { "@types/estree": "*" } },
      "sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==",
    ],

    "@types/cookie": [
      "@types/cookie@0.6.0",
      "",
      {},
      "sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==",
    ],

    "@types/debug": [
      "@types/debug@4.1.12",
      "",
      { "dependencies": { "@types/ms": "*" } },
      "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==",
    ],

    "@types/estree": [
      "@types/estree@1.0.6",
      "",
      {},
      "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==",
    ],

    "@types/estree-jsx": [
      "@types/estree-jsx@1.0.5",
      "",
      { "dependencies": { "@types/estree": "*" } },
      "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",
    ],

    "@types/hast": [
      "@types/hast@3.0.4",
      "",
      { "dependencies": { "@types/unist": "*" } },
      "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",
    ],

    "@types/mdast": [
      "@types/mdast@4.0.4",
      "",
      { "dependencies": { "@types/unist": "*" } },
      "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==",
    ],

    "@types/mdx": [
      "@types/mdx@2.0.13",
      "",
      {},
      "sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw==",
    ],

    "@types/ms": [
      "@types/ms@0.7.34",
      "",
      {},
      "sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==",
    ],

    "@types/nlcst": [
      "@types/nlcst@2.0.3",
      "",
      { "dependencies": { "@types/unist": "*" } },
      "sha512-vSYNSDe6Ix3q+6Z7ri9lyWqgGhJTmzRjZRqyq15N0Z/1/UnVsno9G/N40NBijoYx2seFDIl0+B2mgAb9mezUCA==",
    ],

    "@types/node": [
      "@types/node@17.0.45",
      "",
      {},
      "sha512-w+tIMs3rq2afQdsPJlODhoUEKzFP1ayaoyl1CcnwtIlsVe7K7bA1NGm4s3PraqTLlXnbIN84zuBlxBWo1u9BLw==",
    ],

    "@types/sax": [
      "@types/sax@1.2.7",
      "",
      { "dependencies": { "@types/node": "*" } },
      "sha512-rO73L89PJxeYM3s3pPPjiPgVVcymqU490g0YO5n5By0k2Erzj6tay/4lr1CHAAU4JyOWd1rpQ8bCf6cZfHU96A==",
    ],

    "@types/unist": [
      "@types/unist@3.0.3",
      "",
      {},
      "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==",
    ],

    "@ungap/structured-clone": [
      "@ungap/structured-clone@1.2.1",
      "",
      {},
      "sha512-fEzPV3hSkSMltkw152tJKNARhOupqbH96MZWyRjNaYZOMIzbrTeQDG+MTc6Mr2pgzFQzFxAfmhGDNP5QK++2ZA==",
    ],

    "@volar/kit": [
      "@volar/kit@2.4.11",
      "",
      {
        "dependencies": {
          "@volar/language-service": "2.4.11",
          "@volar/typescript": "2.4.11",
          "typesafe-path": "^0.2.2",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": { "typescript": "*" },
      },
      "sha512-ups5RKbMzMCr6RKafcCqDRnJhJDNWqo2vfekwOAj6psZ15v5TlcQFQAyokQJ3wZxVkzxrQM+TqTRDENfQEXpmA==",
    ],

    "@volar/language-core": [
      "@volar/language-core@2.4.11",
      "",
      { "dependencies": { "@volar/source-map": "2.4.11" } },
      "sha512-lN2C1+ByfW9/JRPpqScuZt/4OrUUse57GLI6TbLgTIqBVemdl1wNcZ1qYGEo2+Gw8coYLgCy7SuKqn6IrQcQgg==",
    ],

    "@volar/language-server": [
      "@volar/language-server@2.4.11",
      "",
      {
        "dependencies": {
          "@volar/language-core": "2.4.11",
          "@volar/language-service": "2.4.11",
          "@volar/typescript": "2.4.11",
          "path-browserify": "^1.0.1",
          "request-light": "^0.7.0",
          "vscode-languageserver": "^9.0.1",
          "vscode-languageserver-protocol": "^3.17.5",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-W9P8glH1M8LGREJ7yHRCANI5vOvTrRO15EMLdmh5WNF9sZYSEbQxiHKckZhvGIkbeR1WAlTl3ORTrJXUghjk7g==",
    ],

    "@volar/language-service": [
      "@volar/language-service@2.4.11",
      "",
      {
        "dependencies": {
          "@volar/language-core": "2.4.11",
          "vscode-languageserver-protocol": "^3.17.5",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-KIb6g8gjUkS2LzAJ9bJCLIjfsJjeRtmXlu7b2pDFGD3fNqdbC53cCAKzgWDs64xtQVKYBU13DLWbtSNFtGuMLQ==",
    ],

    "@volar/source-map": [
      "@volar/source-map@2.4.11",
      "",
      {},
      "sha512-ZQpmafIGvaZMn/8iuvCFGrW3smeqkq/IIh9F1SdSx9aUl0J4Iurzd6/FhmjNO5g2ejF3rT45dKskgXWiofqlZQ==",
    ],

    "@volar/typescript": [
      "@volar/typescript@2.4.11",
      "",
      {
        "dependencies": {
          "@volar/language-core": "2.4.11",
          "path-browserify": "^1.0.1",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-2DT+Tdh88Spp5PyPbqhyoYavYCPDsqbHLFwcUI9K1NlY1YgUJvujGdrqUp0zWxnW7KWNTr3xSpMuv2WnaTKDAw==",
    ],

    "@vscode/emmet-helper": [
      "@vscode/emmet-helper@2.11.0",
      "",
      {
        "dependencies": {
          "emmet": "^2.4.3",
          "jsonc-parser": "^2.3.0",
          "vscode-languageserver-textdocument": "^1.0.1",
          "vscode-languageserver-types": "^3.15.1",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-QLxjQR3imPZPQltfbWRnHU6JecWTF1QSWhx3GAKQpslx7y3Dp6sIIXhKjiUJ/BR9FX8PVthjr9PD6pNwOJfAzw==",
    ],

    "@vscode/l10n": [
      "@vscode/l10n@0.0.18",
      "",
      {},
      "sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==",
    ],

    "acorn": [
      "acorn@8.14.0",
      "",
      { "bin": "bin/acorn" },
      "sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==",
    ],

    "acorn-jsx": [
      "acorn-jsx@5.3.2",
      "",
      { "peerDependencies": { "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0" } },
      "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",
    ],

    "ajv": [
      "ajv@8.17.1",
      "",
      {
        "dependencies": {
          "fast-deep-equal": "^3.1.3",
          "fast-uri": "^3.0.1",
          "json-schema-traverse": "^1.0.0",
          "require-from-string": "^2.0.2",
        },
      },
      "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==",
    ],

    "ansi-align": [
      "ansi-align@3.0.1",
      "",
      { "dependencies": { "string-width": "^4.1.0" } },
      "sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==",
    ],

    "ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "ansi-styles": [
      "ansi-styles@6.2.1",
      "",
      {},
      "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
    ],

    "any-promise": [
      "any-promise@1.3.0",
      "",
      {},
      "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==",
    ],

    "anymatch": [
      "anymatch@3.1.3",
      "",
      { "dependencies": { "normalize-path": "^3.0.0", "picomatch": "^2.0.4" } },
      "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",
    ],

    "arg": [
      "arg@5.0.2",
      "",
      {},
      "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==",
    ],

    "argparse": [
      "argparse@2.0.1",
      "",
      {},
      "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",
    ],

    "aria-query": [
      "aria-query@5.3.2",
      "",
      {},
      "sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==",
    ],

    "array-iterate": [
      "array-iterate@2.0.1",
      "",
      {},
      "sha512-I1jXZMjAgCMmxT4qxXfPXa6SthSoE8h6gkSI9BGGNv8mP8G/v0blc+qFnZu6K42vTOiuME596QaLO0TP3Lk0xg==",
    ],

    "astring": [
      "astring@1.9.0",
      "",
      { "bin": "bin/astring" },
      "sha512-LElXdjswlqjWrPpJFg1Fx4wpkOCxj1TDHlSV4PlaRxHGWko024xICaa97ZkMfs6DRKlCguiAI+rbXv5GWwXIkg==",
    ],

    "astro": [
      "astro@5.2.5",
      "",
      {
        "dependencies": {
          "@astrojs/compiler": "^2.10.3",
          "@astrojs/internal-helpers": "0.5.1",
          "@astrojs/markdown-remark": "6.1.0",
          "@astrojs/telemetry": "3.2.0",
          "@oslojs/encoding": "^1.1.0",
          "@rollup/pluginutils": "^5.1.4",
          "@types/cookie": "^0.6.0",
          "acorn": "^8.14.0",
          "aria-query": "^5.3.2",
          "axobject-query": "^4.1.0",
          "boxen": "8.0.1",
          "ci-info": "^4.1.0",
          "clsx": "^2.1.1",
          "common-ancestor-path": "^1.0.1",
          "cookie": "^0.7.2",
          "cssesc": "^3.0.0",
          "debug": "^4.4.0",
          "deterministic-object-hash": "^2.0.2",
          "devalue": "^5.1.1",
          "diff": "^5.2.0",
          "dlv": "^1.1.3",
          "dset": "^3.1.4",
          "es-module-lexer": "^1.6.0",
          "esbuild": "^0.24.2",
          "estree-walker": "^3.0.3",
          "fast-glob": "^3.3.3",
          "flattie": "^1.1.1",
          "github-slugger": "^2.0.0",
          "html-escaper": "3.0.3",
          "http-cache-semantics": "^4.1.1",
          "js-yaml": "^4.1.0",
          "kleur": "^4.1.5",
          "magic-string": "^0.30.17",
          "magicast": "^0.3.5",
          "micromatch": "^4.0.8",
          "mrmime": "^2.0.0",
          "neotraverse": "^0.6.18",
          "p-limit": "^6.2.0",
          "p-queue": "^8.1.0",
          "preferred-pm": "^4.1.1",
          "prompts": "^2.4.2",
          "rehype": "^13.0.2",
          "semver": "^7.7.1",
          "shiki": "^1.29.2",
          "tinyexec": "^0.3.2",
          "tsconfck": "^3.1.4",
          "ultrahtml": "^1.5.3",
          "unist-util-visit": "^5.0.0",
          "unstorage": "^1.14.4",
          "vfile": "^6.0.3",
          "vite": "^6.0.11",
          "vitefu": "^1.0.5",
          "which-pm": "^3.0.1",
          "xxhash-wasm": "^1.1.0",
          "yargs-parser": "^21.1.1",
          "yocto-spinner": "^0.2.0",
          "zod": "^3.24.1",
          "zod-to-json-schema": "^3.24.1",
          "zod-to-ts": "^1.2.0",
        },
        "optionalDependencies": { "sharp": "^0.33.3" },
        "bin": "astro.js",
      },
      "sha512-AYXyYkc+c5xbKTm48FyQA91y81nXyNPAaoyafR0LUugE4lAwuvIUcXDBfMzmbuP1lGRvsE33G2oypv6gbGaPFg==",
    ],

    "astro-pagefind": [
      "astro-pagefind@1.7.0",
      "",
      {
        "dependencies": {
          "@pagefind/default-ui": "^1.2.0",
          "pagefind": "^1.2.0",
          "sirv": "^3.0.0",
        },
        "peerDependencies": { "astro": "^2.0.4 || ^3 || ^4 || ^5" },
      },
      "sha512-PNcJ2cVIEXsK0toTyJa792uieRQjjXZptPn+0QrMjM6ONHxxiLLeQ0ZtAds/tERlIMcMUyAnVCKhuR0se4gDUw==",
    ],

    "autoprefixer": [
      "autoprefixer@10.4.20",
      "",
      {
        "dependencies": {
          "browserslist": "^4.23.3",
          "caniuse-lite": "^1.0.30001646",
          "fraction.js": "^4.3.7",
          "normalize-range": "^0.1.2",
          "picocolors": "^1.0.1",
          "postcss-value-parser": "^4.2.0",
        },
        "peerDependencies": { "postcss": "^8.1.0" },
        "bin": "bin/autoprefixer",
      },
      "sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==",
    ],

    "axobject-query": [
      "axobject-query@4.1.0",
      "",
      {},
      "sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==",
    ],

    "bail": [
      "bail@2.0.2",
      "",
      {},
      "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==",
    ],

    "balanced-match": [
      "balanced-match@1.0.2",
      "",
      {},
      "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
    ],

    "base-64": [
      "base-64@1.0.0",
      "",
      {},
      "sha512-kwDPIFCGx0NZHog36dj+tHiwP4QMzsZ3AgMViUBKI0+V5n4U0ufTCUMhnQ04diaRI8EX/QcPfql7zlhZ7j4zgg==",
    ],

    "binary-extensions": [
      "binary-extensions@2.3.0",
      "",
      {},
      "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",
    ],

    "boxen": [
      "boxen@8.0.1",
      "",
      {
        "dependencies": {
          "ansi-align": "^3.0.1",
          "camelcase": "^8.0.0",
          "chalk": "^5.3.0",
          "cli-boxes": "^3.0.0",
          "string-width": "^7.2.0",
          "type-fest": "^4.21.0",
          "widest-line": "^5.0.0",
          "wrap-ansi": "^9.0.0",
        },
      },
      "sha512-F3PH5k5juxom4xktynS7MoFY+NUWH5LC4CnH11YB8NPew+HLpmBLCybSAEyb2F+4pRXhuhWqFesoQd6DAyc2hw==",
    ],

    "brace-expansion": [
      "brace-expansion@2.0.1",
      "",
      { "dependencies": { "balanced-match": "^1.0.0" } },
      "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==",
    ],

    "braces": [
      "braces@3.0.3",
      "",
      { "dependencies": { "fill-range": "^7.1.1" } },
      "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
    ],

    "browserslist": [
      "browserslist@4.24.3",
      "",
      {
        "dependencies": {
          "caniuse-lite": "^1.0.30001688",
          "electron-to-chromium": "^1.5.73",
          "node-releases": "^2.0.19",
          "update-browserslist-db": "^1.1.1",
        },
        "bin": "cli.js",
      },
      "sha512-1CPmv8iobE2fyRMV97dAcMVegvvWKxmq94hkLiAkUGwKVTyDLw33K+ZxiFrREKmmps4rIw6grcCFCnTMSZ/YiA==",
    ],

    "camelcase": [
      "camelcase@8.0.0",
      "",
      {},
      "sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==",
    ],

    "camelcase-css": [
      "camelcase-css@2.0.1",
      "",
      {},
      "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==",
    ],

    "caniuse-lite": [
      "caniuse-lite@1.0.30001690",
      "",
      {},
      "sha512-5ExiE3qQN6oF8Clf8ifIDcMRCRE/dMGcETG/XGMD8/XiXm6HXQgQTh1yZYLXXpSOsEUlJm1Xr7kGULZTuGtP/w==",
    ],

    "ccount": [
      "ccount@2.0.1",
      "",
      {},
      "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==",
    ],

    "chalk": [
      "chalk@5.4.1",
      "",
      {},
      "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==",
    ],

    "character-entities": [
      "character-entities@2.0.2",
      "",
      {},
      "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==",
    ],

    "character-entities-html4": [
      "character-entities-html4@2.1.0",
      "",
      {},
      "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==",
    ],

    "character-entities-legacy": [
      "character-entities-legacy@3.0.0",
      "",
      {},
      "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==",
    ],

    "character-reference-invalid": [
      "character-reference-invalid@2.0.1",
      "",
      {},
      "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==",
    ],

    "chokidar": [
      "chokidar@4.0.3",
      "",
      { "dependencies": { "readdirp": "^4.0.1" } },
      "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==",
    ],

    "ci-info": [
      "ci-info@4.1.0",
      "",
      {},
      "sha512-HutrvTNsF48wnxkzERIXOe5/mlcfFcbfCmwcg6CJnizbSue78AbDt+1cgl26zwn61WFxhcPykPfZrbqjGmBb4A==",
    ],

    "cli-boxes": [
      "cli-boxes@3.0.0",
      "",
      {},
      "sha512-/lzGpEWL/8PfI0BmBOPRwp0c/wFNX1RdUML3jK/RcSBA9T8mZDdQpqYBKtCFTOfQbwPqWEOpjqW+Fnayc0969g==",
    ],

    "cliui": [
      "cliui@8.0.1",
      "",
      {
        "dependencies": {
          "string-width": "^4.2.0",
          "strip-ansi": "^6.0.1",
          "wrap-ansi": "^7.0.0",
        },
      },
      "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==",
    ],

    "clsx": [
      "clsx@2.1.1",
      "",
      {},
      "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",
    ],

    "collapse-white-space": [
      "collapse-white-space@2.1.0",
      "",
      {},
      "sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw==",
    ],

    "color": [
      "color@4.2.3",
      "",
      {
        "dependencies": { "color-convert": "^2.0.1", "color-string": "^1.9.0" },
      },
      "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==",
    ],

    "color-convert": [
      "color-convert@2.0.1",
      "",
      { "dependencies": { "color-name": "~1.1.4" } },
      "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
    ],

    "color-name": [
      "color-name@1.1.4",
      "",
      {},
      "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
    ],

    "color-string": [
      "color-string@1.9.1",
      "",
      {
        "dependencies": { "color-name": "^1.0.0", "simple-swizzle": "^0.2.2" },
      },
      "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==",
    ],

    "comma-separated-tokens": [
      "comma-separated-tokens@2.0.3",
      "",
      {},
      "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==",
    ],

    "commander": [
      "commander@4.1.1",
      "",
      {},
      "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==",
    ],

    "common-ancestor-path": [
      "common-ancestor-path@1.0.1",
      "",
      {},
      "sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w==",
    ],

    "consola": [
      "consola@3.3.3",
      "",
      {},
      "sha512-Qil5KwghMzlqd51UXM0b6fyaGHtOC22scxrwrz4A2882LyUMwQjnvaedN1HAeXzphspQ6CpHkzMAWxBTUruDLg==",
    ],

    "cookie": [
      "cookie@0.7.2",
      "",
      {},
      "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==",
    ],

    "cookie-es": [
      "cookie-es@1.2.2",
      "",
      {},
      "sha512-+W7VmiVINB+ywl1HGXJXmrqkOhpKrIiVZV6tQuV54ZyQC7MMuBt81Vc336GMLoHBq5hV/F9eXgt5Mnx0Rha5Fg==",
    ],

    "cross-spawn": [
      "cross-spawn@7.0.6",
      "",
      {
        "dependencies": {
          "path-key": "^3.1.0",
          "shebang-command": "^2.0.0",
          "which": "^2.0.1",
        },
      },
      "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",
    ],

    "crossws": [
      "crossws@0.3.1",
      "",
      { "dependencies": { "uncrypto": "^0.1.3" } },
      "sha512-HsZgeVYaG+b5zA+9PbIPGq4+J/CJynJuearykPsXx4V/eMhyQ5EDVg3Ak2FBZtVXCiOLu/U7IiwDHTr9MA+IKw==",
    ],

    "cssesc": [
      "cssesc@3.0.0",
      "",
      { "bin": "bin/cssesc" },
      "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==",
    ],

    "debug": [
      "debug@4.4.0",
      "",
      { "dependencies": { "ms": "^2.1.3" } },
      "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==",
    ],

    "decode-named-character-reference": [
      "decode-named-character-reference@1.0.2",
      "",
      { "dependencies": { "character-entities": "^2.0.0" } },
      "sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==",
    ],

    "defu": [
      "defu@6.1.4",
      "",
      {},
      "sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==",
    ],

    "dequal": [
      "dequal@2.0.3",
      "",
      {},
      "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==",
    ],

    "destr": [
      "destr@2.0.3",
      "",
      {},
      "sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==",
    ],

    "detect-libc": [
      "detect-libc@2.0.3",
      "",
      {},
      "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==",
    ],

    "deterministic-object-hash": [
      "deterministic-object-hash@2.0.2",
      "",
      { "dependencies": { "base-64": "^1.0.0" } },
      "sha512-KxektNH63SrbfUyDiwXqRb1rLwKt33AmMv+5Nhsw1kqZ13SJBRTgZHtGbE+hH3a1mVW1cz+4pqSWVPAtLVXTzQ==",
    ],

    "devalue": [
      "devalue@5.1.1",
      "",
      {},
      "sha512-maua5KUiapvEwiEAe+XnlZ3Rh0GD+qI1J/nb9vrJc3muPXvcF/8gXYTWF76+5DAqHyDUtOIImEuo0YKE9mshVw==",
    ],

    "devlop": [
      "devlop@1.1.0",
      "",
      { "dependencies": { "dequal": "^2.0.0" } },
      "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",
    ],

    "didyoumean": [
      "didyoumean@1.2.2",
      "",
      {},
      "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==",
    ],

    "diff": [
      "diff@5.2.0",
      "",
      {},
      "sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==",
    ],

    "dlv": [
      "dlv@1.1.3",
      "",
      {},
      "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==",
    ],

    "dset": [
      "dset@3.1.4",
      "",
      {},
      "sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==",
    ],

    "eastasianwidth": [
      "eastasianwidth@0.2.0",
      "",
      {},
      "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==",
    ],

    "electron-to-chromium": [
      "electron-to-chromium@1.5.76",
      "",
      {},
      "sha512-CjVQyG7n7Sr+eBXE86HIulnL5N8xZY1sgmOPGuq/F0Rr0FJq63lg0kEtOIDfZBk44FnDLf6FUJ+dsJcuiUDdDQ==",
    ],

    "emmet": [
      "emmet@2.4.11",
      "",
      {
        "dependencies": {
          "@emmetio/abbreviation": "^2.3.3",
          "@emmetio/css-abbreviation": "^2.1.8",
        },
      },
      "sha512-23QPJB3moh/U9sT4rQzGgeyyGIrcM+GH5uVYg2C6wZIxAIJq7Ng3QLT79tl8FUwDXhyq9SusfknOrofAKqvgyQ==",
    ],

    "emoji-regex": [
      "emoji-regex@8.0.0",
      "",
      {},
      "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
    ],

    "emoji-regex-xs": [
      "emoji-regex-xs@1.0.0",
      "",
      {},
      "sha512-LRlerrMYoIDrT6jgpeZ2YYl/L8EulRTt5hQcYjy5AInh7HWXKimpqx68aknBFpGL2+/IcogTcaydJEgaTmOpDg==",
    ],

    "entities": [
      "entities@4.5.0",
      "",
      {},
      "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==",
    ],

    "es-module-lexer": [
      "es-module-lexer@1.6.0",
      "",
      {},
      "sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==",
    ],

    "esast-util-from-estree": [
      "esast-util-from-estree@2.0.0",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "devlop": "^1.0.0",
          "estree-util-visit": "^2.0.0",
          "unist-util-position-from-estree": "^2.0.0",
        },
      },
      "sha512-4CyanoAudUSBAn5K13H4JhsMH6L9ZP7XbLVe/dKybkxMO7eDyLsT8UHl9TRNrU2Gr9nz+FovfSIjuXWJ81uVwQ==",
    ],

    "esast-util-from-js": [
      "esast-util-from-js@2.0.1",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "acorn": "^8.0.0",
          "esast-util-from-estree": "^2.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-8Ja+rNJ0Lt56Pcf3TAmpBZjmx8ZcK5Ts4cAzIOjsjevg9oSXJnl6SUQ2EevU8tv3h6ZLWmoKL5H4fgWvdvfETw==",
    ],

    "esbuild": [
      "esbuild@0.24.2",
      "",
      {
        "optionalDependencies": {
          "@esbuild/aix-ppc64": "0.24.2",
          "@esbuild/android-arm": "0.24.2",
          "@esbuild/android-arm64": "0.24.2",
          "@esbuild/android-x64": "0.24.2",
          "@esbuild/darwin-arm64": "0.24.2",
          "@esbuild/darwin-x64": "0.24.2",
          "@esbuild/freebsd-arm64": "0.24.2",
          "@esbuild/freebsd-x64": "0.24.2",
          "@esbuild/linux-arm": "0.24.2",
          "@esbuild/linux-arm64": "0.24.2",
          "@esbuild/linux-ia32": "0.24.2",
          "@esbuild/linux-loong64": "0.24.2",
          "@esbuild/linux-mips64el": "0.24.2",
          "@esbuild/linux-ppc64": "0.24.2",
          "@esbuild/linux-riscv64": "0.24.2",
          "@esbuild/linux-s390x": "0.24.2",
          "@esbuild/linux-x64": "0.24.2",
          "@esbuild/netbsd-arm64": "0.24.2",
          "@esbuild/netbsd-x64": "0.24.2",
          "@esbuild/openbsd-arm64": "0.24.2",
          "@esbuild/openbsd-x64": "0.24.2",
          "@esbuild/sunos-x64": "0.24.2",
          "@esbuild/win32-arm64": "0.24.2",
          "@esbuild/win32-ia32": "0.24.2",
          "@esbuild/win32-x64": "0.24.2",
        },
        "bin": "bin/esbuild",
      },
      "sha512-+9egpBW8I3CD5XPe0n6BfT5fxLzxrlDzqydF3aviG+9ni1lDC/OvMHcxqEFV0+LANZG5R1bFMWfUrjVsdwxJvA==",
    ],

    "escalade": [
      "escalade@3.2.0",
      "",
      {},
      "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",
    ],

    "escape-string-regexp": [
      "escape-string-regexp@5.0.0",
      "",
      {},
      "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==",
    ],

    "esprima": [
      "esprima@4.0.1",
      "",
      {
        "bin": {
          "esparse": "bin/esparse.js",
          "esvalidate": "bin/esvalidate.js",
        },
      },
      "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==",
    ],

    "estree-util-attach-comments": [
      "estree-util-attach-comments@3.0.0",
      "",
      { "dependencies": { "@types/estree": "^1.0.0" } },
      "sha512-cKUwm/HUcTDsYh/9FgnuFqpfquUbwIqwKM26BVCGDPVgvaCl/nDCCjUfiLlx6lsEZ3Z4RFxNbOQ60pkaEwFxGw==",
    ],

    "estree-util-build-jsx": [
      "estree-util-build-jsx@3.0.1",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "devlop": "^1.0.0",
          "estree-util-is-identifier-name": "^3.0.0",
          "estree-walker": "^3.0.0",
        },
      },
      "sha512-8U5eiL6BTrPxp/CHbs2yMgP8ftMhR5ww1eIKoWRMlqvltHF8fZn5LRDvTKuxD3DUn+shRbLGqXemcP51oFCsGQ==",
    ],

    "estree-util-is-identifier-name": [
      "estree-util-is-identifier-name@3.0.0",
      "",
      {},
      "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==",
    ],

    "estree-util-scope": [
      "estree-util-scope@1.0.0",
      "",
      { "dependencies": { "@types/estree": "^1.0.0", "devlop": "^1.0.0" } },
      "sha512-2CAASclonf+JFWBNJPndcOpA8EMJwa0Q8LUFJEKqXLW6+qBvbFZuF5gItbQOs/umBUkjviCSDCbBwU2cXbmrhQ==",
    ],

    "estree-util-to-js": [
      "estree-util-to-js@2.0.0",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "astring": "^1.8.0",
          "source-map": "^0.7.0",
        },
      },
      "sha512-WDF+xj5rRWmD5tj6bIqRi6CkLIXbbNQUcxQHzGysQzvHmdYG2G7p/Tf0J0gpxGgkeMZNTIjT/AoSvC9Xehcgdg==",
    ],

    "estree-util-visit": [
      "estree-util-visit@2.0.0",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "@types/unist": "^3.0.0",
        },
      },
      "sha512-m5KgiH85xAhhW8Wta0vShLcUvOsh3LLPI2YVwcbio1l7E09NTLL1EyMZFM1OyWowoH0skScNbhOPl4kcBgzTww==",
    ],

    "estree-walker": [
      "estree-walker@3.0.3",
      "",
      { "dependencies": { "@types/estree": "^1.0.0" } },
      "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==",
    ],

    "eventemitter3": [
      "eventemitter3@5.0.1",
      "",
      {},
      "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==",
    ],

    "extend": [
      "extend@3.0.2",
      "",
      {},
      "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==",
    ],

    "fast-deep-equal": [
      "fast-deep-equal@3.1.3",
      "",
      {},
      "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",
    ],

    "fast-glob": [
      "fast-glob@3.3.3",
      "",
      {
        "dependencies": {
          "@nodelib/fs.stat": "^2.0.2",
          "@nodelib/fs.walk": "^1.2.3",
          "glob-parent": "^5.1.2",
          "merge2": "^1.3.0",
          "micromatch": "^4.0.8",
        },
      },
      "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",
    ],

    "fast-uri": [
      "fast-uri@3.0.3",
      "",
      {},
      "sha512-aLrHthzCjH5He4Z2H9YZ+v6Ujb9ocRuW6ZzkJQOrTxleEijANq4v1TsaPaVG1PZcuurEzrLcWRyYBYXD5cEiaw==",
    ],

    "fast-xml-parser": [
      "fast-xml-parser@4.5.1",
      "",
      {
        "dependencies": { "strnum": "^1.0.5" },
        "bin": { "fxparser": "src/cli/cli.js" },
      },
      "sha512-y655CeyUQ+jj7KBbYMc4FG01V8ZQqjN+gDYGJ50RtfsUB8iG9AmwmwoAgeKLJdmueKKMrH1RJ7yXHTSoczdv5w==",
    ],

    "fastq": [
      "fastq@1.18.0",
      "",
      { "dependencies": { "reusify": "^1.0.4" } },
      "sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==",
    ],

    "fill-range": [
      "fill-range@7.1.1",
      "",
      { "dependencies": { "to-regex-range": "^5.0.1" } },
      "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
    ],

    "find-up": [
      "find-up@4.1.0",
      "",
      { "dependencies": { "locate-path": "^5.0.0", "path-exists": "^4.0.0" } },
      "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==",
    ],

    "find-up-simple": [
      "find-up-simple@1.0.0",
      "",
      {},
      "sha512-q7Us7kcjj2VMePAa02hDAF6d+MzsdsAWEwYyOpwUtlerRBkOEPBCRZrAV4XfcSN8fHAgaD0hP7miwoay6DCprw==",
    ],

    "find-yarn-workspace-root2": [
      "find-yarn-workspace-root2@1.2.16",
      "",
      { "dependencies": { "micromatch": "^4.0.2", "pkg-dir": "^4.2.0" } },
      "sha512-hr6hb1w8ePMpPVUK39S4RlwJzi+xPLuVuG8XlwXU3KD5Yn3qgBWVfy3AzNlDhWvE1EORCE65/Qm26rFQt3VLVA==",
    ],

    "flattie": [
      "flattie@1.1.1",
      "",
      {},
      "sha512-9UbaD6XdAL97+k/n+N7JwX46K/M6Zc6KcFYskrYL8wbBV/Uyk0CTAMY0VT+qiK5PM7AIc9aTWYtq65U7T+aCNQ==",
    ],

    "foreground-child": [
      "foreground-child@3.3.0",
      "",
      { "dependencies": { "cross-spawn": "^7.0.0", "signal-exit": "^4.0.1" } },
      "sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==",
    ],

    "fraction.js": [
      "fraction.js@4.3.7",
      "",
      {},
      "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==",
    ],

    "fsevents": [
      "fsevents@2.3.3",
      "",
      { "os": "darwin" },
      "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
    ],

    "function-bind": [
      "function-bind@1.1.2",
      "",
      {},
      "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
    ],

    "get-caller-file": [
      "get-caller-file@2.0.5",
      "",
      {},
      "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==",
    ],

    "get-east-asian-width": [
      "get-east-asian-width@1.3.0",
      "",
      {},
      "sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==",
    ],

    "github-slugger": [
      "github-slugger@2.0.0",
      "",
      {},
      "sha512-IaOQ9puYtjrkq7Y0Ygl9KDZnrf/aiUJYUpVf89y8kyaxbRG7Y1SrX/jaumrv81vc61+kiMempujsM3Yw7w5qcw==",
    ],

    "glob": [
      "glob@10.4.5",
      "",
      {
        "dependencies": {
          "foreground-child": "^3.1.0",
          "jackspeak": "^3.1.2",
          "minimatch": "^9.0.4",
          "minipass": "^7.1.2",
          "package-json-from-dist": "^1.0.0",
          "path-scurry": "^1.11.1",
        },
        "bin": "dist/esm/bin.mjs",
      },
      "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==",
    ],

    "glob-parent": [
      "glob-parent@6.0.2",
      "",
      { "dependencies": { "is-glob": "^4.0.3" } },
      "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",
    ],

    "graceful-fs": [
      "graceful-fs@4.2.11",
      "",
      {},
      "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==",
    ],

    "h3": [
      "h3@1.13.0",
      "",
      {
        "dependencies": {
          "cookie-es": "^1.2.2",
          "crossws": ">=0.2.0 <0.4.0",
          "defu": "^6.1.4",
          "destr": "^2.0.3",
          "iron-webcrypto": "^1.2.1",
          "ohash": "^1.1.4",
          "radix3": "^1.1.2",
          "ufo": "^1.5.4",
          "uncrypto": "^0.1.3",
          "unenv": "^1.10.0",
        },
      },
      "sha512-vFEAu/yf8UMUcB4s43OaDaigcqpQd14yanmOsn+NcRX3/guSKncyE2rOYhq8RIchgJrPSs/QiIddnTTR1ddiAg==",
    ],

    "hasown": [
      "hasown@2.0.2",
      "",
      { "dependencies": { "function-bind": "^1.1.2" } },
      "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
    ],

    "hast-util-from-html": [
      "hast-util-from-html@2.0.3",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "devlop": "^1.1.0",
          "hast-util-from-parse5": "^8.0.0",
          "parse5": "^7.0.0",
          "vfile": "^6.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-CUSRHXyKjzHov8yKsQjGOElXy/*******************************************/uxM+aLkSPqF/EtMw==",
    ],

    "hast-util-from-parse5": [
      "hast-util-from-parse5@8.0.2",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/unist": "^3.0.0",
          "devlop": "^1.0.0",
          "hastscript": "^9.0.0",
          "property-information": "^6.0.0",
          "vfile": "^6.0.0",
          "vfile-location": "^5.0.0",
          "web-namespaces": "^2.0.0",
        },
      },
      "sha512-SfMzfdAi/zAoZ1KkFEyyeXBn7u/ShQrfd675ZEE9M3qj+PMFX05xubzRyF76CCSJu8au9jgVxDV1+okFvgZU4A==",
    ],

    "hast-util-is-element": [
      "hast-util-is-element@3.0.0",
      "",
      { "dependencies": { "@types/hast": "^3.0.0" } },
      "sha512-Val9mnv2IWpLbNPqc/pUem+a7Ipj2aHacCwgNfTiK0vJKl0LF+4Ba4+v1oPHFpf3bLYmreq0/l3Gud9S5OH42g==",
    ],

    "hast-util-parse-selector": [
      "hast-util-parse-selector@4.0.0",
      "",
      { "dependencies": { "@types/hast": "^3.0.0" } },
      "sha512-wkQCkSYoOGCRKERFWcxMVMOcYE2K1AaNLU8DXS9arxnLOUEWbOXKXiJUNzEpqZ3JOKpnha3jkFrumEjVliDe7A==",
    ],

    "hast-util-raw": [
      "hast-util-raw@9.1.0",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/unist": "^3.0.0",
          "@ungap/structured-clone": "^1.0.0",
          "hast-util-from-parse5": "^8.0.0",
          "hast-util-to-parse5": "^8.0.0",
          "html-void-elements": "^3.0.0",
          "mdast-util-to-hast": "^13.0.0",
          "parse5": "^7.0.0",
          "unist-util-position": "^5.0.0",
          "unist-util-visit": "^5.0.0",
          "vfile": "^6.0.0",
          "web-namespaces": "^2.0.0",
          "zwitch": "^2.0.0",
        },
      },
      "sha512-Y8/SBAHkZGoNkpzqqfCldijcuUKh7/su31kEBp67cFY09Wy0mTRgtsLYsiIxMJxlu0f6AA5SUTbDR8K0rxnbUw==",
    ],

    "hast-util-to-estree": [
      "hast-util-to-estree@3.1.1",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "@types/estree-jsx": "^1.0.0",
          "@types/hast": "^3.0.0",
          "comma-separated-tokens": "^2.0.0",
          "devlop": "^1.0.0",
          "estree-util-attach-comments": "^3.0.0",
          "estree-util-is-identifier-name": "^3.0.0",
          "hast-util-whitespace": "^3.0.0",
          "mdast-util-mdx-expression": "^2.0.0",
          "mdast-util-mdx-jsx": "^3.0.0",
          "mdast-util-mdxjs-esm": "^2.0.0",
          "property-information": "^6.0.0",
          "space-separated-tokens": "^2.0.0",
          "style-to-object": "^1.0.0",
          "unist-util-position": "^5.0.0",
          "zwitch": "^2.0.0",
        },
      },
      "sha512-IWtwwmPskfSmma9RpzCappDUitC8t5jhAynHhc1m2+5trOgsrp7txscUSavc5Ic8PATyAjfrCK1wgtxh2cICVQ==",
    ],

    "hast-util-to-html": [
      "hast-util-to-html@9.0.4",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/unist": "^3.0.0",
          "ccount": "^2.0.0",
          "comma-separated-tokens": "^2.0.0",
          "hast-util-whitespace": "^3.0.0",
          "html-void-elements": "^3.0.0",
          "mdast-util-to-hast": "^13.0.0",
          "property-information": "^6.0.0",
          "space-separated-tokens": "^2.0.0",
          "stringify-entities": "^4.0.0",
          "zwitch": "^2.0.4",
        },
      },
      "sha512-wxQzXtdbhiwGAUKrnQJXlOPmHnEehzphwkK7aluUPQ+lEc1xefC8pblMgpp2w5ldBTEfveRIrADcrhGIWrlTDA==",
    ],

    "hast-util-to-jsx-runtime": [
      "hast-util-to-jsx-runtime@2.3.2",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "@types/hast": "^3.0.0",
          "@types/unist": "^3.0.0",
          "comma-separated-tokens": "^2.0.0",
          "devlop": "^1.0.0",
          "estree-util-is-identifier-name": "^3.0.0",
          "hast-util-whitespace": "^3.0.0",
          "mdast-util-mdx-expression": "^2.0.0",
          "mdast-util-mdx-jsx": "^3.0.0",
          "mdast-util-mdxjs-esm": "^2.0.0",
          "property-information": "^6.0.0",
          "space-separated-tokens": "^2.0.0",
          "style-to-object": "^1.0.0",
          "unist-util-position": "^5.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-1ngXYb+V9UT5h+PxNRa1O1FYguZK/XL+gkeqvp7EdHlB9oHUG0eYRo/vY5inBdcqo3RkPMC58/H94HvkbfGdyg==",
    ],

    "hast-util-to-parse5": [
      "hast-util-to-parse5@8.0.0",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "comma-separated-tokens": "^2.0.0",
          "devlop": "^1.0.0",
          "property-information": "^6.0.0",
          "space-separated-tokens": "^2.0.0",
          "web-namespaces": "^2.0.0",
          "zwitch": "^2.0.0",
        },
      },
      "sha512-3KKrV5ZVI8if87DVSi1vDeByYrkGzg4mEfeu4alwgmmIeARiBLKCZS2uw5Gb6nU9x9Yufyj3iudm6i7nl52PFw==",
    ],

    "hast-util-to-text": [
      "hast-util-to-text@4.0.2",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/unist": "^3.0.0",
          "hast-util-is-element": "^3.0.0",
          "unist-util-find-after": "^5.0.0",
        },
      },
      "sha512-KK6y/BN8lbaq654j7JgBydev7wuNMcID54lkRav1P0CaE1e47P72AWWPiGKXTJU271ooYzcvTAn/Zt0REnvc7A==",
    ],

    "hast-util-whitespace": [
      "hast-util-whitespace@3.0.0",
      "",
      { "dependencies": { "@types/hast": "^3.0.0" } },
      "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",
    ],

    "hastscript": [
      "hastscript@9.0.0",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "comma-separated-tokens": "^2.0.0",
          "hast-util-parse-selector": "^4.0.0",
          "property-information": "^6.0.0",
          "space-separated-tokens": "^2.0.0",
        },
      },
      "sha512-jzaLBGavEDKHrc5EfFImKN7nZKKBdSLIdGvCwDZ9TfzbF2ffXiov8CKE445L2Z1Ek2t/m4SKQ2j6Ipv7NyUolw==",
    ],

    "html-escaper": [
      "html-escaper@3.0.3",
      "",
      {},
      "sha512-RuMffC89BOWQoY0WKGpIhn5gX3iI54O6nRA0yC124NYVtzjmFWBIiFd8M0x+ZdX0P9R4lADg1mgP8C7PxGOWuQ==",
    ],

    "html-void-elements": [
      "html-void-elements@3.0.0",
      "",
      {},
      "sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==",
    ],

    "http-cache-semantics": [
      "http-cache-semantics@4.1.1",
      "",
      {},
      "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==",
    ],

    "import-meta-resolve": [
      "import-meta-resolve@4.1.0",
      "",
      {},
      "sha512-I6fiaX09Xivtk+THaMfAwnA3MVA5Big1WHF1Dfx9hFuvNIWpXnorlkzhcQf6ehrqQiiZECRt1poOAkPmer3ruw==",
    ],

    "inline-style-parser": [
      "inline-style-parser@0.2.4",
      "",
      {},
      "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==",
    ],

    "iron-webcrypto": [
      "iron-webcrypto@1.2.1",
      "",
      {},
      "sha512-feOM6FaSr6rEABp/eDfVseKyTMDt+KGpeB35SkVn9Tyn0CqvVsY3EwI0v5i8nMHyJnzCIQf7nsy3p41TPkJZhg==",
    ],

    "is-alphabetical": [
      "is-alphabetical@2.0.1",
      "",
      {},
      "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==",
    ],

    "is-alphanumerical": [
      "is-alphanumerical@2.0.1",
      "",
      {
        "dependencies": { "is-alphabetical": "^2.0.0", "is-decimal": "^2.0.0" },
      },
      "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",
    ],

    "is-arrayish": [
      "is-arrayish@0.3.2",
      "",
      {},
      "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==",
    ],

    "is-binary-path": [
      "is-binary-path@2.1.0",
      "",
      { "dependencies": { "binary-extensions": "^2.0.0" } },
      "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",
    ],

    "is-core-module": [
      "is-core-module@2.16.1",
      "",
      { "dependencies": { "hasown": "^2.0.2" } },
      "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",
    ],

    "is-decimal": [
      "is-decimal@2.0.1",
      "",
      {},
      "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==",
    ],

    "is-docker": [
      "is-docker@3.0.0",
      "",
      { "bin": "cli.js" },
      "sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==",
    ],

    "is-extglob": [
      "is-extglob@2.1.1",
      "",
      {},
      "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
    ],

    "is-fullwidth-code-point": [
      "is-fullwidth-code-point@3.0.0",
      "",
      {},
      "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
    ],

    "is-glob": [
      "is-glob@4.0.3",
      "",
      { "dependencies": { "is-extglob": "^2.1.1" } },
      "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
    ],

    "is-hexadecimal": [
      "is-hexadecimal@2.0.1",
      "",
      {},
      "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==",
    ],

    "is-inside-container": [
      "is-inside-container@1.0.0",
      "",
      { "dependencies": { "is-docker": "^3.0.0" }, "bin": "cli.js" },
      "sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==",
    ],

    "is-number": [
      "is-number@7.0.0",
      "",
      {},
      "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
    ],

    "is-plain-obj": [
      "is-plain-obj@4.1.0",
      "",
      {},
      "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==",
    ],

    "is-wsl": [
      "is-wsl@3.1.0",
      "",
      { "dependencies": { "is-inside-container": "^1.0.0" } },
      "sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==",
    ],

    "isexe": [
      "isexe@2.0.0",
      "",
      {},
      "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",
    ],

    "jackspeak": [
      "jackspeak@3.4.3",
      "",
      {
        "dependencies": { "@isaacs/cliui": "^8.0.2" },
        "optionalDependencies": { "@pkgjs/parseargs": "^0.11.0" },
      },
      "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==",
    ],

    "jiti": [
      "jiti@1.21.7",
      "",
      { "bin": "bin/jiti.js" },
      "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==",
    ],

    "js-yaml": [
      "js-yaml@4.1.0",
      "",
      { "dependencies": { "argparse": "^2.0.1" }, "bin": "bin/js-yaml.js" },
      "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",
    ],

    "json-schema-traverse": [
      "json-schema-traverse@1.0.0",
      "",
      {},
      "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==",
    ],

    "jsonc-parser": [
      "jsonc-parser@2.3.1",
      "",
      {},
      "sha512-H8jvkz1O50L3dMZCsLqiuB2tA7muqbSg1AtGEkN0leAqGjsUzDJir3Zwr02BhqdcITPg3ei3mZ+HjMocAknhhg==",
    ],

    "kleur": [
      "kleur@4.1.5",
      "",
      {},
      "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==",
    ],

    "lilconfig": [
      "lilconfig@3.1.3",
      "",
      {},
      "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==",
    ],

    "lines-and-columns": [
      "lines-and-columns@1.2.4",
      "",
      {},
      "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==",
    ],

    "load-yaml-file": [
      "load-yaml-file@0.2.0",
      "",
      {
        "dependencies": {
          "graceful-fs": "^4.1.5",
          "js-yaml": "^3.13.0",
          "pify": "^4.0.1",
          "strip-bom": "^3.0.0",
        },
      },
      "sha512-OfCBkGEw4nN6JLtgRidPX6QxjBQGQf72q3si2uvqyFEMbycSFFHwAZeXx6cJgFM9wmLrf9zBwCP3Ivqa+LLZPw==",
    ],

    "locate-path": [
      "locate-path@5.0.0",
      "",
      { "dependencies": { "p-locate": "^4.1.0" } },
      "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==",
    ],

    "lodash": [
      "lodash@4.17.21",
      "",
      {},
      "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==",
    ],

    "lodash.castarray": [
      "lodash.castarray@4.4.0",
      "",
      {},
      "sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==",
    ],

    "lodash.isplainobject": [
      "lodash.isplainobject@4.0.6",
      "",
      {},
      "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==",
    ],

    "lodash.merge": [
      "lodash.merge@4.6.2",
      "",
      {},
      "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==",
    ],

    "longest-streak": [
      "longest-streak@3.1.0",
      "",
      {},
      "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==",
    ],

    "lru-cache": [
      "lru-cache@10.4.3",
      "",
      {},
      "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",
    ],

    "magic-string": [
      "magic-string@0.30.17",
      "",
      { "dependencies": { "@jridgewell/sourcemap-codec": "^1.5.0" } },
      "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==",
    ],

    "magicast": [
      "magicast@0.3.5",
      "",
      {
        "dependencies": {
          "@babel/parser": "^7.25.4",
          "@babel/types": "^7.25.4",
          "source-map-js": "^1.2.0",
        },
      },
      "sha512-L0WhttDl+2BOsybvEOLK7fW3UA0OQ0IQ2d6Zl2x/a6vVRs3bAY0ECOSHHeL5jD+SbOpOCUEi0y1DgHEn9Qn1AQ==",
    ],

    "markdown-extensions": [
      "markdown-extensions@2.0.0",
      "",
      {},
      "sha512-o5vL7aDWatOTX8LzaS1WMoaoxIiLRQJuIKKe2wAw6IeULDHaqbiqiggmx+pKvZDb1Sj+pE46Sn1T7lCqfFtg1Q==",
    ],

    "markdown-table": [
      "markdown-table@3.0.4",
      "",
      {},
      "sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==",
    ],

    "mdast-util-definitions": [
      "mdast-util-definitions@6.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "@types/unist": "^3.0.0",
          "unist-util-visit": "^5.0.0",
        },
      },
      "sha512-scTllyX6pnYNZH/AIp/0ePz6s4cZtARxImwoPJ7kS42n+MnVsI4XbnG6d4ibehRIldYMWM2LD7ImQblVhUejVQ==",
    ],

    "mdast-util-find-and-replace": [
      "mdast-util-find-and-replace@3.0.2",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "escape-string-regexp": "^5.0.0",
          "unist-util-is": "^6.0.0",
          "unist-util-visit-parents": "^6.0.0",
        },
      },
      "sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==",
    ],

    "mdast-util-from-markdown": [
      "mdast-util-from-markdown@2.0.2",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "@types/unist": "^3.0.0",
          "decode-named-character-reference": "^1.0.0",
          "devlop": "^1.0.0",
          "mdast-util-to-string": "^4.0.0",
          "micromark": "^4.0.0",
          "micromark-util-decode-numeric-character-reference": "^2.0.0",
          "micromark-util-decode-string": "^2.0.0",
          "micromark-util-normalize-identifier": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "unist-util-stringify-position": "^4.0.0",
        },
      },
      "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==",
    ],

    "mdast-util-gfm": [
      "mdast-util-gfm@3.0.0",
      "",
      {
        "dependencies": {
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-gfm-autolink-literal": "^2.0.0",
          "mdast-util-gfm-footnote": "^2.0.0",
          "mdast-util-gfm-strikethrough": "^2.0.0",
          "mdast-util-gfm-table": "^2.0.0",
          "mdast-util-gfm-task-list-item": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-dgQEX5Amaq+DuUqf26jJqSK9qgixgd6rYDHAv4aTBuA92cTknZlKpPfa86Z/s8Dj8xsAQpFfBmPUHWJBWqS4Bw==",
    ],

    "mdast-util-gfm-autolink-literal": [
      "mdast-util-gfm-autolink-literal@2.0.1",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "ccount": "^2.0.0",
          "devlop": "^1.0.0",
          "mdast-util-find-and-replace": "^3.0.0",
          "micromark-util-character": "^2.0.0",
        },
      },
      "sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==",
    ],

    "mdast-util-gfm-footnote": [
      "mdast-util-gfm-footnote@2.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "devlop": "^1.1.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
          "micromark-util-normalize-identifier": "^2.0.0",
        },
      },
      "sha512-5jOT2boTSVkMnQ7LTrd6n/18kqwjmuYqo7JUPe+tRCY6O7dAuTFMtTPauYYrMPpox9hlN0uOx/FL8XvEfG9/mQ==",
    ],

    "mdast-util-gfm-strikethrough": [
      "mdast-util-gfm-strikethrough@2.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==",
    ],

    "mdast-util-gfm-table": [
      "mdast-util-gfm-table@2.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "devlop": "^1.0.0",
          "markdown-table": "^3.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==",
    ],

    "mdast-util-gfm-task-list-item": [
      "mdast-util-gfm-task-list-item@2.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "devlop": "^1.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==",
    ],

    "mdast-util-mdx": [
      "mdast-util-mdx@3.0.0",
      "",
      {
        "dependencies": {
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-mdx-expression": "^2.0.0",
          "mdast-util-mdx-jsx": "^3.0.0",
          "mdast-util-mdxjs-esm": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-JfbYLAW7XnYTTbUsmpu0kdBUVe+yKVJZBItEjwyYJiDJuZ9w4eeaqks4HQO+R7objWgS2ymV60GYpI14Ug554w==",
    ],

    "mdast-util-mdx-expression": [
      "mdast-util-mdx-expression@2.0.1",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "@types/hast": "^3.0.0",
          "@types/mdast": "^4.0.0",
          "devlop": "^1.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==",
    ],

    "mdast-util-mdx-jsx": [
      "mdast-util-mdx-jsx@3.1.3",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "@types/hast": "^3.0.0",
          "@types/mdast": "^4.0.0",
          "@types/unist": "^3.0.0",
          "ccount": "^2.0.0",
          "devlop": "^1.1.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
          "parse-entities": "^4.0.0",
          "stringify-entities": "^4.0.0",
          "unist-util-stringify-position": "^4.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-bfOjvNt+1AcbPLTFMFWY149nJz0OjmewJs3LQQ5pIyVGxP4CdOqNVJL6kTaM5c68p8q82Xv3nCyFfUnuEcH3UQ==",
    ],

    "mdast-util-mdxjs-esm": [
      "mdast-util-mdxjs-esm@2.0.1",
      "",
      {
        "dependencies": {
          "@types/estree-jsx": "^1.0.0",
          "@types/hast": "^3.0.0",
          "@types/mdast": "^4.0.0",
          "devlop": "^1.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "mdast-util-to-markdown": "^2.0.0",
        },
      },
      "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",
    ],

    "mdast-util-phrasing": [
      "mdast-util-phrasing@4.1.0",
      "",
      {
        "dependencies": { "@types/mdast": "^4.0.0", "unist-util-is": "^6.0.0" },
      },
      "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",
    ],

    "mdast-util-to-hast": [
      "mdast-util-to-hast@13.2.0",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/mdast": "^4.0.0",
          "@ungap/structured-clone": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-util-sanitize-uri": "^2.0.0",
          "trim-lines": "^3.0.0",
          "unist-util-position": "^5.0.0",
          "unist-util-visit": "^5.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==",
    ],

    "mdast-util-to-markdown": [
      "mdast-util-to-markdown@2.1.2",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "@types/unist": "^3.0.0",
          "longest-streak": "^3.0.0",
          "mdast-util-phrasing": "^4.0.0",
          "mdast-util-to-string": "^4.0.0",
          "micromark-util-classify-character": "^2.0.0",
          "micromark-util-decode-string": "^2.0.0",
          "unist-util-visit": "^5.0.0",
          "zwitch": "^2.0.0",
        },
      },
      "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==",
    ],

    "mdast-util-to-string": [
      "mdast-util-to-string@4.0.0",
      "",
      { "dependencies": { "@types/mdast": "^4.0.0" } },
      "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",
    ],

    "merge2": [
      "merge2@1.4.1",
      "",
      {},
      "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",
    ],

    "micromark": [
      "micromark@4.0.1",
      "",
      {
        "dependencies": {
          "@types/debug": "^4.0.0",
          "debug": "^4.0.0",
          "decode-named-character-reference": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-core-commonmark": "^2.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-chunked": "^2.0.0",
          "micromark-util-combine-extensions": "^2.0.0",
          "micromark-util-decode-numeric-character-reference": "^2.0.0",
          "micromark-util-encode": "^2.0.0",
          "micromark-util-normalize-identifier": "^2.0.0",
          "micromark-util-resolve-all": "^2.0.0",
          "micromark-util-sanitize-uri": "^2.0.0",
          "micromark-util-subtokenize": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-eBPdkcoCNvYcxQOAKAlceo5SNdzZWfF+FcSupREAzdAh9rRmE239CEQAiTwIgblwnoM8zzj35sZ5ZwvSEOF6Kw==",
    ],

    "micromark-core-commonmark": [
      "micromark-core-commonmark@2.0.2",
      "",
      {
        "dependencies": {
          "decode-named-character-reference": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-factory-destination": "^2.0.0",
          "micromark-factory-label": "^2.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-factory-title": "^2.0.0",
          "micromark-factory-whitespace": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-chunked": "^2.0.0",
          "micromark-util-classify-character": "^2.0.0",
          "micromark-util-html-tag-name": "^2.0.0",
          "micromark-util-normalize-identifier": "^2.0.0",
          "micromark-util-resolve-all": "^2.0.0",
          "micromark-util-subtokenize": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-FKjQKbxd1cibWMM1P9N+H8TwlgGgSkWZMmfuVucLCHaYqeSvJ0hFeHsIa65pA2nYbes0f8LDHPMrd9X7Ujxg9w==",
    ],

    "micromark-extension-gfm": [
      "micromark-extension-gfm@3.0.0",
      "",
      {
        "dependencies": {
          "micromark-extension-gfm-autolink-literal": "^2.0.0",
          "micromark-extension-gfm-footnote": "^2.0.0",
          "micromark-extension-gfm-strikethrough": "^2.0.0",
          "micromark-extension-gfm-table": "^2.0.0",
          "micromark-extension-gfm-tagfilter": "^2.0.0",
          "micromark-extension-gfm-task-list-item": "^2.0.0",
          "micromark-util-combine-extensions": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==",
    ],

    "micromark-extension-gfm-autolink-literal": [
      "micromark-extension-gfm-autolink-literal@2.1.0",
      "",
      {
        "dependencies": {
          "micromark-util-character": "^2.0.0",
          "micromark-util-sanitize-uri": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==",
    ],

    "micromark-extension-gfm-footnote": [
      "micromark-extension-gfm-footnote@2.1.0",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-core-commonmark": "^2.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-normalize-identifier": "^2.0.0",
          "micromark-util-sanitize-uri": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==",
    ],

    "micromark-extension-gfm-strikethrough": [
      "micromark-extension-gfm-strikethrough@2.1.0",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-util-chunked": "^2.0.0",
          "micromark-util-classify-character": "^2.0.0",
          "micromark-util-resolve-all": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==",
    ],

    "micromark-extension-gfm-table": [
      "micromark-extension-gfm-table@2.1.0",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-Ub2ncQv+fwD70/l4ou27b4YzfNaCJOvyX4HxXU15m7mpYY+rjuWzsLIPZHJL253Z643RpbcP1oeIJlQ/SKW67g==",
    ],

    "micromark-extension-gfm-tagfilter": [
      "micromark-extension-gfm-tagfilter@2.0.0",
      "",
      { "dependencies": { "micromark-util-types": "^2.0.0" } },
      "sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==",
    ],

    "micromark-extension-gfm-task-list-item": [
      "micromark-extension-gfm-task-list-item@2.1.0",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==",
    ],

    "micromark-extension-mdx-expression": [
      "micromark-extension-mdx-expression@3.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-factory-mdx-expression": "^2.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-events-to-acorn": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-sI0nwhUDz97xyzqJAbHQhp5TfaxEvZZZ2JDqUo+7NvyIYG6BZ5CPPqj2ogUoPJlmXHBnyZUzISg9+oUmU6tUjQ==",
    ],

    "micromark-extension-mdx-jsx": [
      "micromark-extension-mdx-jsx@3.0.1",
      "",
      {
        "dependencies": {
          "@types/acorn": "^4.0.0",
          "@types/estree": "^1.0.0",
          "devlop": "^1.0.0",
          "estree-util-is-identifier-name": "^3.0.0",
          "micromark-factory-mdx-expression": "^2.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-events-to-acorn": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-vNuFb9czP8QCtAQcEJn0UJQJZA8Dk6DXKBqx+bg/w0WGuSxDxNr7hErW89tHUY31dUW4NqEOWwmEUNhjTFmHkg==",
    ],

    "micromark-extension-mdx-md": [
      "micromark-extension-mdx-md@2.0.0",
      "",
      { "dependencies": { "micromark-util-types": "^2.0.0" } },
      "sha512-EpAiszsB3blw4Rpba7xTOUptcFeBFi+6PY8VnJ2hhimH+vCQDirWgsMpz7w1XcZE7LVrSAUGb9VJpG9ghlYvYQ==",
    ],

    "micromark-extension-mdxjs": [
      "micromark-extension-mdxjs@3.0.0",
      "",
      {
        "dependencies": {
          "acorn": "^8.0.0",
          "acorn-jsx": "^5.0.0",
          "micromark-extension-mdx-expression": "^3.0.0",
          "micromark-extension-mdx-jsx": "^3.0.0",
          "micromark-extension-mdx-md": "^2.0.0",
          "micromark-extension-mdxjs-esm": "^3.0.0",
          "micromark-util-combine-extensions": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-A873fJfhnJ2siZyUrJ31l34Uqwy4xIFmvPY1oj+Ean5PHcPBYzEsvqvWGaWcfEIr11O5Dlw3p2y0tZWpKHDejQ==",
    ],

    "micromark-extension-mdxjs-esm": [
      "micromark-extension-mdxjs-esm@3.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-core-commonmark": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-events-to-acorn": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "unist-util-position-from-estree": "^2.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-DJFl4ZqkErRpq/dAPyeWp15tGrcrrJho1hKK5uBS70BCtfrIFg81sqcTVu3Ta+KD1Tk5vAtBNElWxtAa+m8K9A==",
    ],

    "micromark-factory-destination": [
      "micromark-factory-destination@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==",
    ],

    "micromark-factory-label": [
      "micromark-factory-label@2.0.1",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==",
    ],

    "micromark-factory-mdx-expression": [
      "micromark-factory-mdx-expression@2.0.2",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "devlop": "^1.0.0",
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-events-to-acorn": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "unist-util-position-from-estree": "^2.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-5E5I2pFzJyg2CtemqAbcyCktpHXuJbABnsb32wX2U8IQKhhVFBqkcZR5LRm1WVoFqa4kTueZK4abep7wdo9nrw==",
    ],

    "micromark-factory-space": [
      "micromark-factory-space@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-util-character": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==",
    ],

    "micromark-factory-title": [
      "micromark-factory-title@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==",
    ],

    "micromark-factory-whitespace": [
      "micromark-factory-whitespace@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-factory-space": "^2.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==",
    ],

    "micromark-util-character": [
      "micromark-util-character@2.1.1",
      "",
      {
        "dependencies": {
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==",
    ],

    "micromark-util-chunked": [
      "micromark-util-chunked@2.0.1",
      "",
      { "dependencies": { "micromark-util-symbol": "^2.0.0" } },
      "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==",
    ],

    "micromark-util-classify-character": [
      "micromark-util-classify-character@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-util-character": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==",
    ],

    "micromark-util-combine-extensions": [
      "micromark-util-combine-extensions@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-util-chunked": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==",
    ],

    "micromark-util-decode-numeric-character-reference": [
      "micromark-util-decode-numeric-character-reference@2.0.2",
      "",
      { "dependencies": { "micromark-util-symbol": "^2.0.0" } },
      "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==",
    ],

    "micromark-util-decode-string": [
      "micromark-util-decode-string@2.0.1",
      "",
      {
        "dependencies": {
          "decode-named-character-reference": "^1.0.0",
          "micromark-util-character": "^2.0.0",
          "micromark-util-decode-numeric-character-reference": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
        },
      },
      "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==",
    ],

    "micromark-util-encode": [
      "micromark-util-encode@2.0.1",
      "",
      {},
      "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==",
    ],

    "micromark-util-events-to-acorn": [
      "micromark-util-events-to-acorn@2.0.2",
      "",
      {
        "dependencies": {
          "@types/acorn": "^4.0.0",
          "@types/estree": "^1.0.0",
          "@types/unist": "^3.0.0",
          "devlop": "^1.0.0",
          "estree-util-visit": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "vfile-message": "^4.0.0",
        },
      },
      "sha512-Fk+xmBrOv9QZnEDguL9OI9/NQQp6Hz4FuQ4YmCb/5V7+9eAh1s6AYSvL20kHkD67YIg7EpE54TiSlcsf3vyZgA==",
    ],

    "micromark-util-html-tag-name": [
      "micromark-util-html-tag-name@2.0.1",
      "",
      {},
      "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==",
    ],

    "micromark-util-normalize-identifier": [
      "micromark-util-normalize-identifier@2.0.1",
      "",
      { "dependencies": { "micromark-util-symbol": "^2.0.0" } },
      "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==",
    ],

    "micromark-util-resolve-all": [
      "micromark-util-resolve-all@2.0.1",
      "",
      { "dependencies": { "micromark-util-types": "^2.0.0" } },
      "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==",
    ],

    "micromark-util-sanitize-uri": [
      "micromark-util-sanitize-uri@2.0.1",
      "",
      {
        "dependencies": {
          "micromark-util-character": "^2.0.0",
          "micromark-util-encode": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
        },
      },
      "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==",
    ],

    "micromark-util-subtokenize": [
      "micromark-util-subtokenize@2.0.3",
      "",
      {
        "dependencies": {
          "devlop": "^1.0.0",
          "micromark-util-chunked": "^2.0.0",
          "micromark-util-symbol": "^2.0.0",
          "micromark-util-types": "^2.0.0",
        },
      },
      "sha512-VXJJuNxYWSoYL6AJ6OQECCFGhIU2GGHMw8tahogePBrjkG8aCCas3ibkp7RnVOSTClg2is05/R7maAhF1XyQMg==",
    ],

    "micromark-util-symbol": [
      "micromark-util-symbol@2.0.1",
      "",
      {},
      "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==",
    ],

    "micromark-util-types": [
      "micromark-util-types@2.0.1",
      "",
      {},
      "sha512-534m2WhVTddrcKVepwmVEVnUAmtrx9bfIjNoQHRqfnvdaHQiFytEhJoTgpWJvDEXCO5gLTQh3wYC1PgOJA4NSQ==",
    ],

    "micromatch": [
      "micromatch@4.0.8",
      "",
      { "dependencies": { "braces": "^3.0.3", "picomatch": "^2.3.1" } },
      "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",
    ],

    "mime": [
      "mime@3.0.0",
      "",
      { "bin": "cli.js" },
      "sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==",
    ],

    "minimatch": [
      "minimatch@9.0.5",
      "",
      { "dependencies": { "brace-expansion": "^2.0.1" } },
      "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==",
    ],

    "minipass": [
      "minipass@7.1.2",
      "",
      {},
      "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==",
    ],

    "mrmime": [
      "mrmime@2.0.0",
      "",
      {},
      "sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==",
    ],

    "ms": [
      "ms@2.1.3",
      "",
      {},
      "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",
    ],

    "muggle-string": [
      "muggle-string@0.4.1",
      "",
      {},
      "sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==",
    ],

    "mz": [
      "mz@2.7.0",
      "",
      {
        "dependencies": {
          "any-promise": "^1.0.0",
          "object-assign": "^4.0.1",
          "thenify-all": "^1.0.0",
        },
      },
      "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==",
    ],

    "nanoid": [
      "nanoid@3.3.8",
      "",
      { "bin": "bin/nanoid.cjs" },
      "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==",
    ],

    "neotraverse": [
      "neotraverse@0.6.18",
      "",
      {},
      "sha512-Z4SmBUweYa09+o6pG+eASabEpP6QkQ70yHj351pQoEXIs8uHbaU2DWVmzBANKgflPa47A50PtB2+NgRpQvr7vA==",
    ],

    "nlcst-to-string": [
      "nlcst-to-string@4.0.0",
      "",
      { "dependencies": { "@types/nlcst": "^2.0.0" } },
      "sha512-YKLBCcUYKAg0FNlOBT6aI91qFmSiFKiluk655WzPF+DDMA02qIyy8uiRqI8QXtcFpEvll12LpL5MXqEmAZ+dcA==",
    ],

    "node-fetch-native": [
      "node-fetch-native@1.6.4",
      "",
      {},
      "sha512-IhOigYzAKHd244OC0JIMIUrjzctirCmPkaIfhDeGcEETWof5zKYUW7e7MYvChGWh/4CJeXEgsRyGzuF334rOOQ==",
    ],

    "node-releases": [
      "node-releases@2.0.19",
      "",
      {},
      "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",
    ],

    "normalize-path": [
      "normalize-path@3.0.0",
      "",
      {},
      "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",
    ],

    "normalize-range": [
      "normalize-range@0.1.2",
      "",
      {},
      "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==",
    ],

    "object-assign": [
      "object-assign@4.1.1",
      "",
      {},
      "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",
    ],

    "object-hash": [
      "object-hash@3.0.0",
      "",
      {},
      "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==",
    ],

    "ofetch": [
      "ofetch@1.4.1",
      "",
      {
        "dependencies": {
          "destr": "^2.0.3",
          "node-fetch-native": "^1.6.4",
          "ufo": "^1.5.4",
        },
      },
      "sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==",
    ],

    "ohash": [
      "ohash@1.1.4",
      "",
      {},
      "sha512-FlDryZAahJmEF3VR3w1KogSEdWX3WhA5GPakFx4J81kEAiHyLMpdLLElS8n8dfNadMgAne/MywcvmogzscVt4g==",
    ],

    "oniguruma-to-es": [
      "oniguruma-to-es@2.3.0",
      "",
      {
        "dependencies": {
          "emoji-regex-xs": "^1.0.0",
          "regex": "^5.1.1",
          "regex-recursion": "^5.1.1",
        },
      },
      "sha512-bwALDxriqfKGfUufKGGepCzu9x7nJQuoRoAFp4AnwehhC2crqrDIAP/uN2qdlsAvSMpeRC3+Yzhqc7hLmle5+g==",
    ],

    "p-limit": [
      "p-limit@6.2.0",
      "",
      { "dependencies": { "yocto-queue": "^1.1.1" } },
      "sha512-kuUqqHNUqoIWp/c467RI4X6mmyuojY5jGutNU0wVTmEOOfcuwLqyMVoAi9MKi2Ak+5i9+nhmrK4ufZE8069kHA==",
    ],

    "p-locate": [
      "p-locate@4.1.0",
      "",
      { "dependencies": { "p-limit": "^2.2.0" } },
      "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==",
    ],

    "p-queue": [
      "p-queue@8.1.0",
      "",
      { "dependencies": { "eventemitter3": "^5.0.1", "p-timeout": "^6.1.2" } },
      "sha512-mxLDbbGIBEXTJL0zEx8JIylaj3xQ7Z/7eEVjcF9fJX4DBiH9oqe+oahYnlKKxm0Ci9TlWTyhSHgygxMxjIB2jw==",
    ],

    "p-timeout": [
      "p-timeout@6.1.4",
      "",
      {},
      "sha512-MyIV3ZA/PmyBN/ud8vV9XzwTrNtR4jFrObymZYnZqMmW0zA8Z17vnT0rBgFE/TlohB+YCHqXMgZzb3Csp49vqg==",
    ],

    "p-try": [
      "p-try@2.2.0",
      "",
      {},
      "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==",
    ],

    "package-json-from-dist": [
      "package-json-from-dist@1.0.1",
      "",
      {},
      "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==",
    ],

    "pagefind": [
      "pagefind@1.3.0",
      "",
      {
        "optionalDependencies": {
          "@pagefind/darwin-arm64": "1.3.0",
          "@pagefind/darwin-x64": "1.3.0",
          "@pagefind/linux-arm64": "1.3.0",
          "@pagefind/linux-x64": "1.3.0",
          "@pagefind/windows-x64": "1.3.0",
        },
        "bin": "lib/runner/bin.cjs",
      },
      "sha512-8KPLGT5g9s+olKMRTU9LFekLizkVIu9tes90O1/aigJ0T5LmyPqTzGJrETnSw3meSYg58YH7JTzhTTW/3z6VAw==",
    ],

    "parse-entities": [
      "parse-entities@4.0.2",
      "",
      {
        "dependencies": {
          "@types/unist": "^2.0.0",
          "character-entities-legacy": "^3.0.0",
          "character-reference-invalid": "^2.0.0",
          "decode-named-character-reference": "^1.0.0",
          "is-alphanumerical": "^2.0.0",
          "is-decimal": "^2.0.0",
          "is-hexadecimal": "^2.0.0",
        },
      },
      "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==",
    ],

    "parse-latin": [
      "parse-latin@7.0.0",
      "",
      {
        "dependencies": {
          "@types/nlcst": "^2.0.0",
          "@types/unist": "^3.0.0",
          "nlcst-to-string": "^4.0.0",
          "unist-util-modify-children": "^4.0.0",
          "unist-util-visit-children": "^3.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-mhHgobPPua5kZ98EF4HWiH167JWBfl4pvAIXXdbaVohtK7a6YBOy56kvhCqduqyo/f3yrHFWmqmiMg/BkBkYYQ==",
    ],

    "parse5": [
      "parse5@7.2.1",
      "",
      { "dependencies": { "entities": "^4.5.0" } },
      "sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==",
    ],

    "path-browserify": [
      "path-browserify@1.0.1",
      "",
      {},
      "sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==",
    ],

    "path-exists": [
      "path-exists@4.0.0",
      "",
      {},
      "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",
    ],

    "path-key": [
      "path-key@3.1.1",
      "",
      {},
      "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
    ],

    "path-parse": [
      "path-parse@1.0.7",
      "",
      {},
      "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",
    ],

    "path-scurry": [
      "path-scurry@1.11.1",
      "",
      {
        "dependencies": {
          "lru-cache": "^10.2.0",
          "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0",
        },
      },
      "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==",
    ],

    "pathe": [
      "pathe@1.1.2",
      "",
      {},
      "sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==",
    ],

    "picocolors": [
      "picocolors@1.1.1",
      "",
      {},
      "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",
    ],

    "picomatch": [
      "picomatch@4.0.2",
      "",
      {},
      "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==",
    ],

    "pify": [
      "pify@4.0.1",
      "",
      {},
      "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==",
    ],

    "pirates": [
      "pirates@4.0.6",
      "",
      {},
      "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==",
    ],

    "pkg-dir": [
      "pkg-dir@4.2.0",
      "",
      { "dependencies": { "find-up": "^4.0.0" } },
      "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==",
    ],

    "postcss": [
      "postcss@8.5.1",
      "",
      {
        "dependencies": {
          "nanoid": "^3.3.8",
          "picocolors": "^1.1.1",
          "source-map-js": "^1.2.1",
        },
      },
      "sha512-6oz2beyjc5VMn/KV1pPw8fliQkhBXrVn1Z3TVyqZxU8kZpzEKhBdmCFqI6ZbmGtamQvQGuU1sgPTk8ZrXDD7jQ==",
    ],

    "postcss-import": [
      "postcss-import@15.1.0",
      "",
      {
        "dependencies": {
          "postcss-value-parser": "^4.0.0",
          "read-cache": "^1.0.0",
          "resolve": "^1.1.7",
        },
        "peerDependencies": { "postcss": "^8.0.0" },
      },
      "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==",
    ],

    "postcss-js": [
      "postcss-js@4.0.1",
      "",
      {
        "dependencies": { "camelcase-css": "^2.0.1" },
        "peerDependencies": { "postcss": "^8.4.21" },
      },
      "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==",
    ],

    "postcss-load-config": [
      "postcss-load-config@4.0.2",
      "",
      {
        "dependencies": { "lilconfig": "^3.0.0", "yaml": "^2.3.4" },
        "peerDependencies": { "postcss": ">=8.0.9", "ts-node": ">=9.0.0" },
        "optionalPeers": ["ts-node"],
      },
      "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==",
    ],

    "postcss-nested": [
      "postcss-nested@6.2.0",
      "",
      {
        "dependencies": { "postcss-selector-parser": "^6.1.1" },
        "peerDependencies": { "postcss": "^8.2.14" },
      },
      "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==",
    ],

    "postcss-selector-parser": [
      "postcss-selector-parser@6.0.10",
      "",
      { "dependencies": { "cssesc": "^3.0.0", "util-deprecate": "^1.0.2" } },
      "sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==",
    ],

    "postcss-value-parser": [
      "postcss-value-parser@4.2.0",
      "",
      {},
      "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==",
    ],

    "preferred-pm": [
      "preferred-pm@4.1.1",
      "",
      {
        "dependencies": {
          "find-up-simple": "^1.0.0",
          "find-yarn-workspace-root2": "1.2.16",
          "which-pm": "^3.0.1",
        },
      },
      "sha512-rU+ZAv1Ur9jAUZtGPebQVQPzdGhNzaEiQ7VL9+cjsAWPHFYOccNXPNiev1CCDSOg/2j7UujM7ojNhpkuILEVNQ==",
    ],

    "prettier": [
      "prettier@3.4.2",
      "",
      { "bin": "bin/prettier.cjs" },
      "sha512-e9MewbtFo+Fevyuxn/4rrcDAaq0IYxPGLvObpQjiZBMAzB9IGmzlnG9RZy3FFas+eBMu2vA0CszMeduow5dIuQ==",
    ],

    "prettier-plugin-astro": [
      "prettier-plugin-astro@0.14.1",
      "",
      {
        "dependencies": {
          "@astrojs/compiler": "^2.9.1",
          "prettier": "^3.0.0",
          "sass-formatter": "^0.7.6",
        },
      },
      "sha512-RiBETaaP9veVstE4vUwSIcdATj6dKmXljouXc/DDNwBSPTp8FRkLGDSGFClKsAFeeg+13SB0Z1JZvbD76bigJw==",
    ],

    "prettier-plugin-tailwindcss": [
      "prettier-plugin-tailwindcss@0.6.9",
      "",
      {
        "peerDependencies": {
          "@ianvs/prettier-plugin-sort-imports": "*",
          "@prettier/plugin-pug": "*",
          "@shopify/prettier-plugin-liquid": "*",
          "@trivago/prettier-plugin-sort-imports": "*",
          "@zackad/prettier-plugin-twig-melody": "*",
          "prettier": "^3.0",
          "prettier-plugin-astro": "*",
          "prettier-plugin-css-order": "*",
          "prettier-plugin-import-sort": "*",
          "prettier-plugin-jsdoc": "*",
          "prettier-plugin-marko": "*",
          "prettier-plugin-multiline-arrays": "*",
          "prettier-plugin-organize-attributes": "*",
          "prettier-plugin-organize-imports": "*",
          "prettier-plugin-sort-imports": "*",
          "prettier-plugin-style-order": "*",
          "prettier-plugin-svelte": "*",
        },
        "optionalPeers": [
          "@ianvs/prettier-plugin-sort-imports",
          "@prettier/plugin-pug",
          "@shopify/prettier-plugin-liquid",
          "@trivago/prettier-plugin-sort-imports",
          "@zackad/prettier-plugin-twig-melody",
          "prettier-plugin-css-order",
          "prettier-plugin-import-sort",
          "prettier-plugin-jsdoc",
          "prettier-plugin-marko",
          "prettier-plugin-multiline-arrays",
          "prettier-plugin-organize-attributes",
          "prettier-plugin-organize-imports",
          "prettier-plugin-sort-imports",
          "prettier-plugin-style-order",
          "prettier-plugin-svelte",
        ],
      },
      "sha512-r0i3uhaZAXYP0At5xGfJH876W3HHGHDp+LCRUJrs57PBeQ6mYHMwr25KH8NPX44F2yGTvdnH7OqCshlQx183Eg==",
    ],

    "prismjs": [
      "prismjs@1.29.0",
      "",
      {},
      "sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q==",
    ],

    "prompts": [
      "prompts@2.4.2",
      "",
      { "dependencies": { "kleur": "^3.0.3", "sisteransi": "^1.0.5" } },
      "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==",
    ],

    "property-information": [
      "property-information@6.5.0",
      "",
      {},
      "sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==",
    ],

    "queue-microtask": [
      "queue-microtask@1.2.3",
      "",
      {},
      "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",
    ],

    "radix3": [
      "radix3@1.1.2",
      "",
      {},
      "sha512-b484I/7b8rDEdSDKckSSBA8knMpcdsXudlE/LNL639wFoHKwLbEkQFZHWEYwDC0wa0FKUcCY+GAF73Z7wxNVFA==",
    ],

    "read-cache": [
      "read-cache@1.0.0",
      "",
      { "dependencies": { "pify": "^2.3.0" } },
      "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==",
    ],

    "readdirp": [
      "readdirp@4.0.2",
      "",
      {},
      "sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==",
    ],

    "recma-build-jsx": [
      "recma-build-jsx@1.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "estree-util-build-jsx": "^3.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-8GtdyqaBcDfva+GUKDr3nev3VpKAhup1+RvkMvUxURHpW7QyIvk9F5wz7Vzo06CEMSilw6uArgRqhpiUcWp8ew==",
    ],

    "recma-jsx": [
      "recma-jsx@1.0.0",
      "",
      {
        "dependencies": {
          "acorn-jsx": "^5.0.0",
          "estree-util-to-js": "^2.0.0",
          "recma-parse": "^1.0.0",
          "recma-stringify": "^1.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-5vwkv65qWwYxg+Atz95acp8DMu1JDSqdGkA2Of1j6rCreyFUE/gp15fC8MnGEuG1W68UKjM6x6+YTWIh7hZM/Q==",
    ],

    "recma-parse": [
      "recma-parse@1.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "esast-util-from-js": "^2.0.0",
          "unified": "^11.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-OYLsIGBB5Y5wjnSnQW6t3Xg7q3fQ7FWbw/vcXtORTnyaSFscOtABg+7Pnz6YZ6c27fG1/aN8CjfwoUEUIdwqWQ==",
    ],

    "recma-stringify": [
      "recma-stringify@1.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "estree-util-to-js": "^2.0.0",
          "unified": "^11.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-cjwII1MdIIVloKvC9ErQ+OgAtwHBmcZ0Bg4ciz78FtbT8In39aAYbaA7zvxQ61xVMSPE8WxhLwLbhif4Js2C+g==",
    ],

    "regex": [
      "regex@5.1.1",
      "",
      { "dependencies": { "regex-utilities": "^2.3.0" } },
      "sha512-dN5I359AVGPnwzJm2jN1k0W9LPZ+ePvoOeVMMfqIMFz53sSwXkxaJoxr50ptnsC771lK95BnTrVSZxq0b9yCGw==",
    ],

    "regex-recursion": [
      "regex-recursion@5.1.1",
      "",
      { "dependencies": { "regex": "^5.1.1", "regex-utilities": "^2.3.0" } },
      "sha512-ae7SBCbzVNrIjgSbh7wMznPcQel1DNlDtzensnFxpiNpXt1U2ju/bHugH422r+4LAVS1FpW1YCwilmnNsjum9w==",
    ],

    "regex-utilities": [
      "regex-utilities@2.3.0",
      "",
      {},
      "sha512-8VhliFJAWRaUiVvREIiW2NXXTmHs4vMNnSzuJVhscgmGav3g9VDxLrQndI3dZZVVdp0ZO/5v0xmX516/7M9cng==",
    ],

    "rehype": [
      "rehype@13.0.2",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "rehype-parse": "^9.0.0",
          "rehype-stringify": "^10.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-j31mdaRFrwFRUIlxGeuPXXKWQxet52RBQRvCmzl5eCefn/KGbomK5GMHNMsOJf55fgo3qw5tST5neDuarDYR2A==",
    ],

    "rehype-parse": [
      "rehype-parse@9.0.1",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "hast-util-from-html": "^2.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-ksCzCD0Fgfh7trPDxr2rSylbwq9iYDkSn8TCDmEJ49ljEUBxDVCzCHv7QNzZOfODanX4+bWQ4WZqLCRWYLfhag==",
    ],

    "rehype-raw": [
      "rehype-raw@7.0.0",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "hast-util-raw": "^9.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-/aE8hCfKlQeA8LmyeyQvQF3eBiLRGNlfBJEvWH7ivp9sBqs7TNqBL5X3v157rM4IFETqDnIOO+z5M/biZbo9Ww==",
    ],

    "rehype-recma": [
      "rehype-recma@1.0.0",
      "",
      {
        "dependencies": {
          "@types/estree": "^1.0.0",
          "@types/hast": "^3.0.0",
          "hast-util-to-estree": "^3.0.0",
        },
      },
      "sha512-lqA4rGUf1JmacCNWWZx0Wv1dHqMwxzsDWYMTowuplHF3xH0N/MmrZ/G3BDZnzAkRmxDadujCjaKM2hqYdCBOGw==",
    ],

    "rehype-stringify": [
      "rehype-stringify@10.0.1",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "hast-util-to-html": "^9.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-k9ecfXHmIPuFVI61B9DeLPN0qFHfawM6RsuX48hoqlaKSF61RskNjSm1lI8PhBEM0MRdLxVVm4WmTqJQccH9mA==",
    ],

    "remark-gfm": [
      "remark-gfm@4.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "mdast-util-gfm": "^3.0.0",
          "micromark-extension-gfm": "^3.0.0",
          "remark-parse": "^11.0.0",
          "remark-stringify": "^11.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-U92vJgBPkbw4Zfu/IiW2oTZLSL3Zpv+uI7My2eq8JxKgqraFdU8YUGicEJCEgSbeaG+QDFqIcwwfMTOEelPxuA==",
    ],

    "remark-mdx": [
      "remark-mdx@3.1.0",
      "",
      {
        "dependencies": {
          "mdast-util-mdx": "^3.0.0",
          "micromark-extension-mdxjs": "^3.0.0",
        },
      },
      "sha512-Ngl/H3YXyBV9RcRNdlYsZujAmhsxwzxpDzpDEhFBVAGthS4GDgnctpDjgFl/ULx5UEDzqtW1cyBSNKqYYrqLBA==",
    ],

    "remark-parse": [
      "remark-parse@11.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "mdast-util-from-markdown": "^2.0.0",
          "micromark-util-types": "^2.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==",
    ],

    "remark-rehype": [
      "remark-rehype@11.1.1",
      "",
      {
        "dependencies": {
          "@types/hast": "^3.0.0",
          "@types/mdast": "^4.0.0",
          "mdast-util-to-hast": "^13.0.0",
          "unified": "^11.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-g/osARvjkBXb6Wo0XvAeXQohVta8i84ACbenPpoSsxTOQH/Ae0/RGP4WZgnMH5pMLpsj4FG7OHmcIcXxpza8eQ==",
    ],

    "remark-smartypants": [
      "remark-smartypants@3.0.2",
      "",
      {
        "dependencies": {
          "retext": "^9.0.0",
          "retext-smartypants": "^6.0.0",
          "unified": "^11.0.4",
          "unist-util-visit": "^5.0.0",
        },
      },
      "sha512-ILTWeOriIluwEvPjv67v7Blgrcx+LZOkAUVtKI3putuhlZm84FnqDORNXPPm+HY3NdZOMhyDwZ1E+eZB/Df5dA==",
    ],

    "remark-stringify": [
      "remark-stringify@11.0.0",
      "",
      {
        "dependencies": {
          "@types/mdast": "^4.0.0",
          "mdast-util-to-markdown": "^2.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==",
    ],

    "request-light": [
      "request-light@0.7.0",
      "",
      {},
      "sha512-lMbBMrDoxgsyO+yB3sDcrDuX85yYt7sS8BfQd11jtbW/z5ZWgLZRcEGLsLoYw7I0WSUGQBs8CC8ScIxkTX1+6Q==",
    ],

    "require-directory": [
      "require-directory@2.1.1",
      "",
      {},
      "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==",
    ],

    "require-from-string": [
      "require-from-string@2.0.2",
      "",
      {},
      "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==",
    ],

    "resolve": [
      "resolve@1.22.10",
      "",
      {
        "dependencies": {
          "is-core-module": "^2.16.0",
          "path-parse": "^1.0.7",
          "supports-preserve-symlinks-flag": "^1.0.0",
        },
        "bin": "bin/resolve",
      },
      "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==",
    ],

    "retext": [
      "retext@9.0.0",
      "",
      {
        "dependencies": {
          "@types/nlcst": "^2.0.0",
          "retext-latin": "^4.0.0",
          "retext-stringify": "^4.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-sbMDcpHCNjvlheSgMfEcVrZko3cDzdbe1x/e7G66dFp0Ff7Mldvi2uv6JkJQzdRcvLYE8CA8Oe8siQx8ZOgTcA==",
    ],

    "retext-latin": [
      "retext-latin@4.0.0",
      "",
      {
        "dependencies": {
          "@types/nlcst": "^2.0.0",
          "parse-latin": "^7.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-hv9woG7Fy0M9IlRQloq/N6atV82NxLGveq+3H2WOi79dtIYWN8OaxogDm77f8YnVXJL2VD3bbqowu5E3EMhBYA==",
    ],

    "retext-smartypants": [
      "retext-smartypants@6.2.0",
      "",
      {
        "dependencies": {
          "@types/nlcst": "^2.0.0",
          "nlcst-to-string": "^4.0.0",
          "unist-util-visit": "^5.0.0",
        },
      },
      "sha512-kk0jOU7+zGv//kfjXEBjdIryL1Acl4i9XNkHxtM7Tm5lFiCog576fjNC9hjoR7LTKQ0DsPWy09JummSsH1uqfQ==",
    ],

    "retext-stringify": [
      "retext-stringify@4.0.0",
      "",
      {
        "dependencies": {
          "@types/nlcst": "^2.0.0",
          "nlcst-to-string": "^4.0.0",
          "unified": "^11.0.0",
        },
      },
      "sha512-rtfN/0o8kL1e+78+uxPTqu1Klt0yPzKuQ2BfWwwfgIUSayyzxpM1PJzkKt4V8803uB9qSy32MvI7Xep9khTpiA==",
    ],

    "reusify": [
      "reusify@1.0.4",
      "",
      {},
      "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==",
    ],

    "rollup": [
      "rollup@4.34.6",
      "",
      {
        "dependencies": { "@types/estree": "1.0.6" },
        "optionalDependencies": {
          "@rollup/rollup-android-arm-eabi": "4.34.6",
          "@rollup/rollup-android-arm64": "4.34.6",
          "@rollup/rollup-darwin-arm64": "4.34.6",
          "@rollup/rollup-darwin-x64": "4.34.6",
          "@rollup/rollup-freebsd-arm64": "4.34.6",
          "@rollup/rollup-freebsd-x64": "4.34.6",
          "@rollup/rollup-linux-arm-gnueabihf": "4.34.6",
          "@rollup/rollup-linux-arm-musleabihf": "4.34.6",
          "@rollup/rollup-linux-arm64-gnu": "4.34.6",
          "@rollup/rollup-linux-arm64-musl": "4.34.6",
          "@rollup/rollup-linux-loongarch64-gnu": "4.34.6",
          "@rollup/rollup-linux-powerpc64le-gnu": "4.34.6",
          "@rollup/rollup-linux-riscv64-gnu": "4.34.6",
          "@rollup/rollup-linux-s390x-gnu": "4.34.6",
          "@rollup/rollup-linux-x64-gnu": "4.34.6",
          "@rollup/rollup-linux-x64-musl": "4.34.6",
          "@rollup/rollup-win32-arm64-msvc": "4.34.6",
          "@rollup/rollup-win32-ia32-msvc": "4.34.6",
          "@rollup/rollup-win32-x64-msvc": "4.34.6",
          "fsevents": "~2.3.2",
        },
        "bin": "dist/bin/rollup",
      },
      "sha512-wc2cBWqJgkU3Iz5oztRkQbfVkbxoz5EhnCGOrnJvnLnQ7O0WhQUYyv18qQI79O8L7DdHrrlJNeCHd4VGpnaXKQ==",
    ],

    "run-parallel": [
      "run-parallel@1.2.0",
      "",
      { "dependencies": { "queue-microtask": "^1.2.2" } },
      "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",
    ],

    "s.color": [
      "s.color@0.0.15",
      "",
      {},
      "sha512-AUNrbEUHeKY8XsYr/DYpl+qk5+aM+DChopnWOPEzn8YKzOhv4l2zH6LzZms3tOZP3wwdOyc0RmTciyi46HLIuA==",
    ],

    "sass-formatter": [
      "sass-formatter@0.7.9",
      "",
      { "dependencies": { "suf-log": "^2.5.3" } },
      "sha512-CWZ8XiSim+fJVG0cFLStwDvft1VI7uvXdCNJYXhDvowiv+DsbD1nXLiQ4zrE5UBvj5DWZJ93cwN0NX5PMsr1Pw==",
    ],

    "sax": [
      "sax@1.4.1",
      "",
      {},
      "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==",
    ],

    "semver": [
      "semver@7.7.1",
      "",
      { "bin": "bin/semver.js" },
      "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
    ],

    "sharp": [
      "sharp@0.33.5",
      "",
      {
        "dependencies": {
          "color": "^4.2.3",
          "detect-libc": "^2.0.3",
          "semver": "^7.6.3",
        },
        "optionalDependencies": {
          "@img/sharp-darwin-arm64": "0.33.5",
          "@img/sharp-darwin-x64": "0.33.5",
          "@img/sharp-libvips-darwin-arm64": "1.0.4",
          "@img/sharp-libvips-darwin-x64": "1.0.4",
          "@img/sharp-libvips-linux-arm": "1.0.5",
          "@img/sharp-libvips-linux-arm64": "1.0.4",
          "@img/sharp-libvips-linux-s390x": "1.0.4",
          "@img/sharp-libvips-linux-x64": "1.0.4",
          "@img/sharp-libvips-linuxmusl-arm64": "1.0.4",
          "@img/sharp-libvips-linuxmusl-x64": "1.0.4",
          "@img/sharp-linux-arm": "0.33.5",
          "@img/sharp-linux-arm64": "0.33.5",
          "@img/sharp-linux-s390x": "0.33.5",
          "@img/sharp-linux-x64": "0.33.5",
          "@img/sharp-linuxmusl-arm64": "0.33.5",
          "@img/sharp-linuxmusl-x64": "0.33.5",
          "@img/sharp-wasm32": "0.33.5",
          "@img/sharp-win32-ia32": "0.33.5",
          "@img/sharp-win32-x64": "0.33.5",
        },
      },
      "sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==",
    ],

    "shebang-command": [
      "shebang-command@2.0.0",
      "",
      { "dependencies": { "shebang-regex": "^3.0.0" } },
      "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",
    ],

    "shebang-regex": [
      "shebang-regex@3.0.0",
      "",
      {},
      "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",
    ],

    "shiki": [
      "shiki@1.29.2",
      "",
      {
        "dependencies": {
          "@shikijs/core": "1.29.2",
          "@shikijs/engine-javascript": "1.29.2",
          "@shikijs/engine-oniguruma": "1.29.2",
          "@shikijs/langs": "1.29.2",
          "@shikijs/themes": "1.29.2",
          "@shikijs/types": "1.29.2",
          "@shikijs/vscode-textmate": "^10.0.1",
          "@types/hast": "^3.0.4",
        },
      },
      "sha512-njXuliz/cP+67jU2hukkxCNuH1yUi4QfdZZY+sMr5PPrIyXSu5iTb/qYC4BiWWB0vZ+7TbdvYUCeL23zpwCfbg==",
    ],

    "signal-exit": [
      "signal-exit@4.1.0",
      "",
      {},
      "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",
    ],

    "simple-swizzle": [
      "simple-swizzle@0.2.2",
      "",
      { "dependencies": { "is-arrayish": "^0.3.1" } },
      "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==",
    ],

    "sirv": [
      "sirv@3.0.0",
      "",
      {
        "dependencies": {
          "@polka/url": "^1.0.0-next.24",
          "mrmime": "^2.0.0",
          "totalist": "^3.0.0",
        },
      },
      "sha512-BPwJGUeDaDCHihkORDchNyyTvWFhcusy1XMmhEVTQTwGeybFbp8YEmB+njbPnth1FibULBSBVwCQni25XlCUDg==",
    ],

    "sisteransi": [
      "sisteransi@1.0.5",
      "",
      {},
      "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==",
    ],

    "sitemap": [
      "sitemap@8.0.0",
      "",
      {
        "dependencies": {
          "@types/node": "^17.0.5",
          "@types/sax": "^1.2.1",
          "arg": "^5.0.0",
          "sax": "^1.2.4",
        },
        "bin": "dist/cli.js",
      },
      "sha512-+AbdxhM9kJsHtruUF39bwS/B0Fytw6Fr1o4ZAIAEqA6cke2xcoO2GleBw9Zw7nRzILVEgz7zBM5GiTJjie1G9A==",
    ],

    "smol-toml": [
      "smol-toml@1.3.1",
      "",
      {},
      "sha512-tEYNll18pPKHroYSmLLrksq233j021G0giwW7P3D24jC54pQ5W5BXMsQ/Mvw1OJCmEYDgY+lrzT+3nNUtoNfXQ==",
    ],

    "source-map": [
      "source-map@0.7.4",
      "",
      {},
      "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==",
    ],

    "source-map-js": [
      "source-map-js@1.2.1",
      "",
      {},
      "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==",
    ],

    "space-separated-tokens": [
      "space-separated-tokens@2.0.2",
      "",
      {},
      "sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==",
    ],

    "sprintf-js": [
      "sprintf-js@1.0.3",
      "",
      {},
      "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==",
    ],

    "stream-replace-string": [
      "stream-replace-string@2.0.0",
      "",
      {},
      "sha512-TlnjJ1C0QrmxRNrON00JvaFFlNh5TTG00APw23j74ET7gkQpTASi6/L2fuiav8pzK715HXtUeClpBTw2NPSn6w==",
    ],

    "string-width": [
      "string-width@4.2.3",
      "",
      {
        "dependencies": {
          "emoji-regex": "^8.0.0",
          "is-fullwidth-code-point": "^3.0.0",
          "strip-ansi": "^6.0.1",
        },
      },
      "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
    ],

    "string-width-cjs": [
      "string-width@4.2.3",
      "",
      {
        "dependencies": {
          "emoji-regex": "^8.0.0",
          "is-fullwidth-code-point": "^3.0.0",
          "strip-ansi": "^6.0.1",
        },
      },
      "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
    ],

    "stringify-entities": [
      "stringify-entities@4.0.4",
      "",
      {
        "dependencies": {
          "character-entities-html4": "^2.0.0",
          "character-entities-legacy": "^3.0.0",
        },
      },
      "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==",
    ],

    "strip-ansi": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "strip-ansi-cjs": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "strip-bom": [
      "strip-bom@3.0.0",
      "",
      {},
      "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==",
    ],

    "strnum": [
      "strnum@1.0.5",
      "",
      {},
      "sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==",
    ],

    "style-to-object": [
      "style-to-object@1.0.8",
      "",
      { "dependencies": { "inline-style-parser": "0.2.4" } },
      "sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==",
    ],

    "sucrase": [
      "sucrase@3.35.0",
      "",
      {
        "dependencies": {
          "@jridgewell/gen-mapping": "^0.3.2",
          "commander": "^4.0.0",
          "glob": "^10.3.10",
          "lines-and-columns": "^1.1.6",
          "mz": "^2.7.0",
          "pirates": "^4.0.1",
          "ts-interface-checker": "^0.1.9",
        },
        "bin": { "sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node" },
      },
      "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==",
    ],

    "suf-log": [
      "suf-log@2.5.3",
      "",
      { "dependencies": { "s.color": "0.0.15" } },
      "sha512-KvC8OPjzdNOe+xQ4XWJV2whQA0aM1kGVczMQ8+dStAO6KfEB140JEVQ9dE76ONZ0/Ylf67ni4tILPJB41U0eow==",
    ],

    "supports-preserve-symlinks-flag": [
      "supports-preserve-symlinks-flag@1.0.0",
      "",
      {},
      "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",
    ],

    "tailwind-merge": [
      "tailwind-merge@2.6.0",
      "",
      {},
      "sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==",
    ],

    "tailwindcss": [
      "tailwindcss@3.4.17",
      "",
      {
        "dependencies": {
          "@alloc/quick-lru": "^5.2.0",
          "arg": "^5.0.2",
          "chokidar": "^3.6.0",
          "didyoumean": "^1.2.2",
          "dlv": "^1.1.3",
          "fast-glob": "^3.3.2",
          "glob-parent": "^6.0.2",
          "is-glob": "^4.0.3",
          "jiti": "^1.21.6",
          "lilconfig": "^3.1.3",
          "micromatch": "^4.0.8",
          "normalize-path": "^3.0.0",
          "object-hash": "^3.0.0",
          "picocolors": "^1.1.1",
          "postcss": "^8.4.47",
          "postcss-import": "^15.1.0",
          "postcss-js": "^4.0.1",
          "postcss-load-config": "^4.0.2",
          "postcss-nested": "^6.2.0",
          "postcss-selector-parser": "^6.1.2",
          "resolve": "^1.22.8",
          "sucrase": "^3.35.0",
        },
        "bin": { "tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js" },
      },
      "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==",
    ],

    "thenify": [
      "thenify@3.3.1",
      "",
      { "dependencies": { "any-promise": "^1.0.0" } },
      "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==",
    ],

    "thenify-all": [
      "thenify-all@1.6.0",
      "",
      { "dependencies": { "thenify": ">= 3.1.0 < 4" } },
      "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==",
    ],

    "tinyexec": [
      "tinyexec@0.3.2",
      "",
      {},
      "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==",
    ],

    "to-regex-range": [
      "to-regex-range@5.0.1",
      "",
      { "dependencies": { "is-number": "^7.0.0" } },
      "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",
    ],

    "totalist": [
      "totalist@3.0.1",
      "",
      {},
      "sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==",
    ],

    "trim-lines": [
      "trim-lines@3.0.1",
      "",
      {},
      "sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==",
    ],

    "trough": [
      "trough@2.2.0",
      "",
      {},
      "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==",
    ],

    "ts-interface-checker": [
      "ts-interface-checker@0.1.13",
      "",
      {},
      "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==",
    ],

    "tsconfck": [
      "tsconfck@3.1.4",
      "",
      {
        "peerDependencies": { "typescript": "^5.0.0" },
        "bin": "bin/tsconfck.js",
      },
      "sha512-kdqWFGVJqe+KGYvlSO9NIaWn9jT1Ny4oKVzAJsKii5eoE9snzTJzL4+MMVOMn+fikWGFmKEylcXL710V/kIPJQ==",
    ],

    "tslib": [
      "tslib@2.8.1",
      "",
      {},
      "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==",
    ],

    "type-fest": [
      "type-fest@4.31.0",
      "",
      {},
      "sha512-yCxltHW07Nkhv/1F6wWBr8kz+5BGMfP+RbRSYFnegVb0qV/UMT0G0ElBloPVerqn4M2ZV80Ir1FtCcYv1cT6vQ==",
    ],

    "typesafe-path": [
      "typesafe-path@0.2.2",
      "",
      {},
      "sha512-OJabfkAg1WLZSqJAJ0Z6Sdt3utnbzr/jh+NAHoyWHJe8CMSy79Gm085094M9nvTPy22KzTVn5Zq5mbapCI/hPA==",
    ],

    "typescript": [
      "typescript@5.7.2",
      "",
      { "bin": { "tsc": "bin/tsc", "tsserver": "bin/tsserver" } },
      "sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==",
    ],

    "typescript-auto-import-cache": [
      "typescript-auto-import-cache@0.3.5",
      "",
      { "dependencies": { "semver": "^7.3.8" } },
      "sha512-fAIveQKsoYj55CozUiBoj4b/7WpN0i4o74wiGY5JVUEoD0XiqDk1tJqTEjgzL2/AizKQrXxyRosSebyDzBZKjw==",
    ],

    "ufo": [
      "ufo@1.5.4",
      "",
      {},
      "sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==",
    ],

    "ultrahtml": [
      "ultrahtml@1.5.3",
      "",
      {},
      "sha512-GykOvZwgDWZlTQMtp5jrD4BVL+gNn2NVlVafjcFUJ7taY20tqYdwdoWBFy6GBJsNTZe1GkGPkSl5knQAjtgceg==",
    ],

    "uncrypto": [
      "uncrypto@0.1.3",
      "",
      {},
      "sha512-Ql87qFHB3s/De2ClA9e0gsnS6zXG27SkTiSJwjCc9MebbfapQfuPzumMIUMi38ezPZVNFcHI9sUIepeQfw8J8Q==",
    ],

    "undici-types": [
      "undici-types@6.20.0",
      "",
      {},
      "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==",
    ],

    "unenv": [
      "unenv@1.10.0",
      "",
      {
        "dependencies": {
          "consola": "^3.2.3",
          "defu": "^6.1.4",
          "mime": "^3.0.0",
          "node-fetch-native": "^1.6.4",
          "pathe": "^1.1.2",
        },
      },
      "sha512-wY5bskBQFL9n3Eca5XnhH6KbUo/tfvkwm9OpcdCvLaeA7piBNbavbOKJySEwQ1V0RH6HvNlSAFRTpvTqgKRQXQ==",
    ],

    "unified": [
      "unified@11.0.5",
      "",
      {
        "dependencies": {
          "@types/unist": "^3.0.0",
          "bail": "^2.0.0",
          "devlop": "^1.0.0",
          "extend": "^3.0.0",
          "is-plain-obj": "^4.0.0",
          "trough": "^2.0.0",
          "vfile": "^6.0.0",
        },
      },
      "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==",
    ],

    "unist-util-find-after": [
      "unist-util-find-after@5.0.0",
      "",
      {
        "dependencies": { "@types/unist": "^3.0.0", "unist-util-is": "^6.0.0" },
      },
      "sha512-amQa0Ep2m6hE2g72AugUItjbuM8X8cGQnFoHk0pGfrFeT9GZhzN5SW8nRsiGKK7Aif4CrACPENkA6P/Lw6fHGQ==",
    ],

    "unist-util-is": [
      "unist-util-is@6.0.0",
      "",
      { "dependencies": { "@types/unist": "^3.0.0" } },
      "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
    ],

    "unist-util-modify-children": [
      "unist-util-modify-children@4.0.0",
      "",
      {
        "dependencies": { "@types/unist": "^3.0.0", "array-iterate": "^2.0.0" },
      },
      "sha512-+tdN5fGNddvsQdIzUF3Xx82CU9sMM+fA0dLgR9vOmT0oPT2jH+P1nd5lSqfCfXAw+93NhcXNY2qqvTUtE4cQkw==",
    ],

    "unist-util-position": [
      "unist-util-position@5.0.0",
      "",
      { "dependencies": { "@types/unist": "^3.0.0" } },
      "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==",
    ],

    "unist-util-position-from-estree": [
      "unist-util-position-from-estree@2.0.0",
      "",
      { "dependencies": { "@types/unist": "^3.0.0" } },
      "sha512-KaFVRjoqLyF6YXCbVLNad/eS4+OfPQQn2yOd7zF/h5T/CSL2v8NpN6a5TPvtbXthAGw5nG+PuTtq+DdIZr+cRQ==",
    ],

    "unist-util-remove-position": [
      "unist-util-remove-position@5.0.0",
      "",
      {
        "dependencies": {
          "@types/unist": "^3.0.0",
          "unist-util-visit": "^5.0.0",
        },
      },
      "sha512-Hp5Kh3wLxv0PHj9m2yZhhLt58KzPtEYKQQ4yxfYFEO7EvHwzyDYnduhHnY1mDxoqr7VUwVuHXk9RXKIiYS1N8Q==",
    ],

    "unist-util-stringify-position": [
      "unist-util-stringify-position@4.0.0",
      "",
      { "dependencies": { "@types/unist": "^3.0.0" } },
      "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
    ],

    "unist-util-visit": [
      "unist-util-visit@5.0.0",
      "",
      {
        "dependencies": {
          "@types/unist": "^3.0.0",
          "unist-util-is": "^6.0.0",
          "unist-util-visit-parents": "^6.0.0",
        },
      },
      "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
    ],

    "unist-util-visit-children": [
      "unist-util-visit-children@3.0.0",
      "",
      { "dependencies": { "@types/unist": "^3.0.0" } },
      "sha512-RgmdTfSBOg04sdPcpTSD1jzoNBjt9a80/ZCzp5cI9n1qPzLZWF9YdvWGN2zmTumP1HWhXKdUWexjy/Wy/lJ7tA==",
    ],

    "unist-util-visit-parents": [
      "unist-util-visit-parents@6.0.1",
      "",
      {
        "dependencies": { "@types/unist": "^3.0.0", "unist-util-is": "^6.0.0" },
      },
      "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
    ],

    "unstorage": [
      "unstorage@1.14.4",
      "",
      {
        "dependencies": {
          "anymatch": "^3.1.3",
          "chokidar": "^3.6.0",
          "destr": "^2.0.3",
          "h3": "^1.13.0",
          "lru-cache": "^10.4.3",
          "node-fetch-native": "^1.6.4",
          "ofetch": "^1.4.1",
          "ufo": "^1.5.4",
        },
        "peerDependencies": {
          "@azure/app-configuration": "^1.8.0",
          "@azure/cosmos": "^4.2.0",
          "@azure/data-tables": "^13.3.0",
          "@azure/identity": "^4.5.0",
          "@azure/keyvault-secrets": "^4.9.0",
          "@azure/storage-blob": "^12.26.0",
          "@capacitor/preferences": "^6.0.3",
          "@deno/kv": ">=0.8.4",
          "@netlify/blobs": "^6.5.0 || ^7.0.0 || ^8.1.0",
          "@planetscale/database": "^1.19.0",
          "@upstash/redis": "^1.34.3",
          "@vercel/blob": ">=0.27.0",
          "@vercel/kv": "^1.0.1",
          "aws4fetch": "^1.0.20",
          "db0": ">=0.2.1",
          "idb-keyval": "^6.2.1",
          "ioredis": "^5.4.2",
          "uploadthing": "^7.4.1",
        },
        "optionalPeers": [
          "@azure/app-configuration",
          "@azure/cosmos",
          "@azure/data-tables",
          "@azure/identity",
          "@azure/keyvault-secrets",
          "@azure/storage-blob",
          "@capacitor/preferences",
          "@deno/kv",
          "@netlify/blobs",
          "@planetscale/database",
          "@upstash/redis",
          "@vercel/blob",
          "@vercel/kv",
          "aws4fetch",
          "db0",
          "idb-keyval",
          "ioredis",
          "uploadthing",
        ],
      },
      "sha512-1SYeamwuYeQJtJ/USE1x4l17LkmQBzg7deBJ+U9qOBoHo15d1cDxG4jM31zKRgF7pG0kirZy4wVMX6WL6Zoscg==",
    ],

    "update-browserslist-db": [
      "update-browserslist-db@1.1.1",
      "",
      {
        "dependencies": { "escalade": "^3.2.0", "picocolors": "^1.1.0" },
        "peerDependencies": { "browserslist": ">= 4.21.0" },
        "bin": "cli.js",
      },
      "sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==",
    ],

    "util-deprecate": [
      "util-deprecate@1.0.2",
      "",
      {},
      "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==",
    ],

    "vfile": [
      "vfile@6.0.3",
      "",
      {
        "dependencies": { "@types/unist": "^3.0.0", "vfile-message": "^4.0.0" },
      },
      "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==",
    ],

    "vfile-location": [
      "vfile-location@5.0.3",
      "",
      { "dependencies": { "@types/unist": "^3.0.0", "vfile": "^6.0.0" } },
      "sha512-5yXvWDEgqeiYiBe1lbxYF7UMAIm/IcopxMHrMQDq3nvKcjPKIhZklUKL+AE7J7uApI4kwe2snsK+eI6UTj9EHg==",
    ],

    "vfile-message": [
      "vfile-message@4.0.2",
      "",
      {
        "dependencies": {
          "@types/unist": "^3.0.0",
          "unist-util-stringify-position": "^4.0.0",
        },
      },
      "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
    ],

    "vite": [
      "vite@6.1.0",
      "",
      {
        "dependencies": {
          "esbuild": "^0.24.2",
          "postcss": "^8.5.1",
          "rollup": "^4.30.1",
        },
        "optionalDependencies": { "fsevents": "~2.3.3" },
        "peerDependencies": {
          "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0",
          "jiti": ">=1.21.0",
          "less": "*",
          "lightningcss": "^1.21.0",
          "sass": "*",
          "sass-embedded": "*",
          "stylus": "*",
          "sugarss": "*",
          "terser": "^5.16.0",
          "tsx": "^4.8.1",
          "yaml": "^2.4.2",
        },
        "optionalPeers": [
          "less",
          "lightningcss",
          "sass",
          "sass-embedded",
          "stylus",
          "sugarss",
          "terser",
          "tsx",
        ],
        "bin": "bin/vite.js",
      },
      "sha512-RjjMipCKVoR4hVfPY6GQTgveinjNuyLw+qruksLDvA5ktI1150VmcMBKmQaEWJhg/j6Uaf6dNCNA0AfdzUb/hQ==",
    ],

    "vitefu": [
      "vitefu@1.0.5",
      "",
      {
        "peerDependencies": { "vite": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0" },
      },
      "sha512-h4Vflt9gxODPFNGPwp4zAMZRpZR7eslzwH2c5hn5kNZ5rhnKyRJ50U+yGCdc2IRaBs8O4haIgLNGrV5CrpMsCA==",
    ],

    "volar-service-css": [
      "volar-service-css@0.0.62",
      "",
      {
        "dependencies": {
          "vscode-css-languageservice": "^6.3.0",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-JwNyKsH3F8PuzZYuqPf+2e+4CTU8YoyUHEHVnoXNlrLe7wy9U3biomZ56llN69Ris7TTy/+DEX41yVxQpM4qvg==",
    ],

    "volar-service-emmet": [
      "volar-service-emmet@0.0.62",
      "",
      {
        "dependencies": {
          "@emmetio/css-parser": "^0.4.0",
          "@emmetio/html-matcher": "^1.3.0",
          "@vscode/emmet-helper": "^2.9.3",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-U4dxWDBWz7Pi4plpbXf4J4Z/ss6kBO3TYrACxWNsE29abu75QzVS0paxDDhI6bhqpbDFXlpsDhZ9aXVFpnfGRQ==",
    ],

    "volar-service-html": [
      "volar-service-html@0.0.62",
      "",
      {
        "dependencies": {
          "vscode-html-languageservice": "^5.3.0",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-Zw01aJsZRh4GTGUjveyfEzEqpULQUdQH79KNEiKVYHZyuGtdBRYCHlrus1sueSNMxwwkuF5WnOHfvBzafs8yyQ==",
    ],

    "volar-service-prettier": [
      "volar-service-prettier@0.0.62",
      "",
      {
        "dependencies": { "vscode-uri": "^3.0.8" },
        "peerDependencies": {
          "@volar/language-service": "~2.4.0",
          "prettier": "^2.2 || ^3.0",
        },
      },
      "sha512-h2yk1RqRTE+vkYZaI9KYuwpDfOQRrTEMvoHol0yW4GFKc75wWQRrb5n/5abDrzMPrkQbSip8JH2AXbvrRtYh4w==",
    ],

    "volar-service-typescript": [
      "volar-service-typescript@0.0.62",
      "",
      {
        "dependencies": {
          "path-browserify": "^1.0.1",
          "semver": "^7.6.2",
          "typescript-auto-import-cache": "^0.3.3",
          "vscode-languageserver-textdocument": "^1.0.11",
          "vscode-nls": "^5.2.0",
          "vscode-uri": "^3.0.8",
        },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-p7MPi71q7KOsH0eAbZwPBiKPp9B2+qrdHAd6VY5oTo9BUXatsOAdakTm9Yf0DUj6uWBAaOT01BSeVOPwucMV1g==",
    ],

    "volar-service-typescript-twoslash-queries": [
      "volar-service-typescript-twoslash-queries@0.0.62",
      "",
      {
        "dependencies": { "vscode-uri": "^3.0.8" },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-KxFt4zydyJYYI0kFAcWPTh4u0Ha36TASPZkAnNY784GtgajerUqM80nX/W1d0wVhmcOFfAxkVsf/Ed+tiYU7ng==",
    ],

    "volar-service-yaml": [
      "volar-service-yaml@0.0.62",
      "",
      {
        "dependencies": {
          "vscode-uri": "^3.0.8",
          "yaml-language-server": "~1.15.0",
        },
        "peerDependencies": { "@volar/language-service": "~2.4.0" },
      },
      "sha512-k7gvv7sk3wa+nGll3MaSKyjwQsJjIGCHFjVkl3wjaSP2nouKyn9aokGmqjrl39mi88Oy49giog2GkZH526wjig==",
    ],

    "vscode-css-languageservice": [
      "vscode-css-languageservice@6.3.2",
      "",
      {
        "dependencies": {
          "@vscode/l10n": "^0.0.18",
          "vscode-languageserver-textdocument": "^1.0.12",
          "vscode-languageserver-types": "3.17.5",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-GEpPxrUTAeXWdZWHev1OJU9lz2Q2/PPBxQ2TIRmLGvQiH3WZbqaNoute0n0ewxlgtjzTW3AKZT+NHySk5Rf4Eg==",
    ],

    "vscode-html-languageservice": [
      "vscode-html-languageservice@5.3.1",
      "",
      {
        "dependencies": {
          "@vscode/l10n": "^0.0.18",
          "vscode-languageserver-textdocument": "^1.0.12",
          "vscode-languageserver-types": "^3.17.5",
          "vscode-uri": "^3.0.8",
        },
      },
      "sha512-ysUh4hFeW/WOWz/TO9gm08xigiSsV/FOAZ+DolgJfeLftna54YdmZ4A+lIn46RbdO3/Qv5QHTn1ZGqmrXQhZyA==",
    ],

    "vscode-json-languageservice": [
      "vscode-json-languageservice@4.1.8",
      "",
      {
        "dependencies": {
          "jsonc-parser": "^3.0.0",
          "vscode-languageserver-textdocument": "^1.0.1",
          "vscode-languageserver-types": "^3.16.0",
          "vscode-nls": "^5.0.0",
          "vscode-uri": "^3.0.2",
        },
      },
      "sha512-0vSpg6Xd9hfV+eZAaYN63xVVMOTmJ4GgHxXnkLCh+9RsQBkWKIghzLhW2B9ebfG+LQQg8uLtsQ2aUKjTgE+QOg==",
    ],

    "vscode-jsonrpc": [
      "vscode-jsonrpc@8.2.0",
      "",
      {},
      "sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==",
    ],

    "vscode-languageserver": [
      "vscode-languageserver@9.0.1",
      "",
      {
        "dependencies": { "vscode-languageserver-protocol": "3.17.5" },
        "bin": {
          "installServerIntoExtension": "bin/installServerIntoExtension",
        },
      },
      "sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==",
    ],

    "vscode-languageserver-protocol": [
      "vscode-languageserver-protocol@3.17.5",
      "",
      {
        "dependencies": {
          "vscode-jsonrpc": "8.2.0",
          "vscode-languageserver-types": "3.17.5",
        },
      },
      "sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==",
    ],

    "vscode-languageserver-textdocument": [
      "vscode-languageserver-textdocument@1.0.12",
      "",
      {},
      "sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==",
    ],

    "vscode-languageserver-types": [
      "vscode-languageserver-types@3.17.5",
      "",
      {},
      "sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==",
    ],

    "vscode-nls": [
      "vscode-nls@5.2.0",
      "",
      {},
      "sha512-RAaHx7B14ZU04EU31pT+rKz2/zSl7xMsfIZuo8pd+KZO6PXtQmpevpq3vxvWNcrGbdmhM/rr5Uw5Mz+NBfhVng==",
    ],

    "vscode-uri": [
      "vscode-uri@3.0.8",
      "",
      {},
      "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==",
    ],

    "web-namespaces": [
      "web-namespaces@2.0.1",
      "",
      {},
      "sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==",
    ],

    "which": [
      "which@2.0.2",
      "",
      {
        "dependencies": { "isexe": "^2.0.0" },
        "bin": { "node-which": "bin/node-which" },
      },
      "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",
    ],

    "which-pm": [
      "which-pm@3.0.1",
      "",
      { "dependencies": { "load-yaml-file": "^0.2.0" } },
      "sha512-v2JrMq0waAI4ju1xU5x3blsxBBMgdgZve580iYMN5frDaLGjbA24fok7wKCsya8KLVO19Ju4XDc5+zTZCJkQfg==",
    ],

    "which-pm-runs": [
      "which-pm-runs@1.1.0",
      "",
      {},
      "sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==",
    ],

    "widest-line": [
      "widest-line@5.0.0",
      "",
      { "dependencies": { "string-width": "^7.0.0" } },
      "sha512-c9bZp7b5YtRj2wOe6dlj32MK+Bx/M/d+9VB2SHM1OtsUHR0aV0tdP6DWh/iMt0kWi1t5g1Iudu6hQRNd1A4PVA==",
    ],

    "wrap-ansi": [
      "wrap-ansi@9.0.0",
      "",
      {
        "dependencies": {
          "ansi-styles": "^6.2.1",
          "string-width": "^7.0.0",
          "strip-ansi": "^7.1.0",
        },
      },
      "sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==",
    ],

    "wrap-ansi-cjs": [
      "wrap-ansi@7.0.0",
      "",
      {
        "dependencies": {
          "ansi-styles": "^4.0.0",
          "string-width": "^4.1.0",
          "strip-ansi": "^6.0.0",
        },
      },
      "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
    ],

    "xxhash-wasm": [
      "xxhash-wasm@1.1.0",
      "",
      {},
      "sha512-147y/6YNh+tlp6nd/2pWq38i9h6mz/EuQ6njIrmW8D1BS5nCqs0P6DG+m6zTGnNz5I+uhZ0SHxBs9BsPrwcKDA==",
    ],

    "y18n": [
      "y18n@5.0.8",
      "",
      {},
      "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==",
    ],

    "yaml": [
      "yaml@2.7.0",
      "",
      { "bin": "bin.mjs" },
      "sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==",
    ],

    "yaml-language-server": [
      "yaml-language-server@1.15.0",
      "",
      {
        "dependencies": {
          "ajv": "^8.11.0",
          "lodash": "4.17.21",
          "request-light": "^0.5.7",
          "vscode-json-languageservice": "4.1.8",
          "vscode-languageserver": "^7.0.0",
          "vscode-languageserver-textdocument": "^1.0.1",
          "vscode-languageserver-types": "^3.16.0",
          "vscode-nls": "^5.0.0",
          "vscode-uri": "^3.0.2",
          "yaml": "2.2.2",
        },
        "optionalDependencies": { "prettier": "2.8.7" },
        "bin": "bin/yaml-language-server",
      },
      "sha512-N47AqBDCMQmh6mBLmI6oqxryHRzi33aPFPsJhYy3VTUGCdLHYjGh4FZzpUjRlphaADBBkDmnkM/++KNIOHi5Rw==",
    ],

    "yargs": [
      "yargs@17.7.2",
      "",
      {
        "dependencies": {
          "cliui": "^8.0.1",
          "escalade": "^3.1.1",
          "get-caller-file": "^2.0.5",
          "require-directory": "^2.1.1",
          "string-width": "^4.2.3",
          "y18n": "^5.0.5",
          "yargs-parser": "^21.1.1",
        },
      },
      "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==",
    ],

    "yargs-parser": [
      "yargs-parser@21.1.1",
      "",
      {},
      "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==",
    ],

    "yocto-queue": [
      "yocto-queue@1.1.1",
      "",
      {},
      "sha512-b4JR1PFR10y1mKjhHY9LaGo6tmrgjit7hxVIeAmyMw3jegXR4dhYqLaQF5zMXZxY7tLpMyJeLjr1C4rLmkVe8g==",
    ],

    "yocto-spinner": [
      "yocto-spinner@0.2.0",
      "",
      { "dependencies": { "yoctocolors": "^2.1.1" } },
      "sha512-Qu6WAqNLGleB687CCGcmgHIo8l+J19MX/32UrSMfbf/4L8gLoxjpOYoiHT1asiWyqvjRZbgvOhLlvne6E5Tbdw==",
    ],

    "yoctocolors": [
      "yoctocolors@2.1.1",
      "",
      {},
      "sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ==",
    ],

    "zod": [
      "zod@3.24.1",
      "",
      {},
      "sha512-muH7gBL9sI1nciMZV67X5fTKKBLtwpZ5VBp1vsOQzj1MhrBZ4wlVCm3gedKZWLp0Oyel8sIGfeiz54Su+OVT+A==",
    ],

    "zod-to-json-schema": [
      "zod-to-json-schema@3.24.1",
      "",
      { "peerDependencies": { "zod": "^3.24.1" } },
      "sha512-3h08nf3Vw3Wl3PK+q3ow/lIil81IT2Oa7YpQyUUDsEWbXveMesdfK1xBd2RhCkynwZndAxixji/7SYJJowr62w==",
    ],

    "zod-to-ts": [
      "zod-to-ts@1.2.0",
      "",
      { "peerDependencies": { "typescript": "^4.9.4 || ^5.0.2", "zod": "^3" } },
      "sha512-x30XE43V+InwGpvTySRNz9kB7qFU8DlyEy7BsSTCHPH1R0QasMmHWZDCzYm6bVXtj/9NNJAZF3jW8rzFvH5OFA==",
    ],

    "zwitch": [
      "zwitch@2.0.4",
      "",
      {},
      "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==",
    ],

    "@isaacs/cliui/string-width": [
      "string-width@5.1.2",
      "",
      {
        "dependencies": {
          "eastasianwidth": "^0.2.0",
          "emoji-regex": "^9.2.2",
          "strip-ansi": "^7.0.1",
        },
      },
      "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==",
    ],

    "@isaacs/cliui/strip-ansi": [
      "strip-ansi@7.1.0",
      "",
      { "dependencies": { "ansi-regex": "^6.0.1" } },
      "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
    ],

    "@isaacs/cliui/wrap-ansi": [
      "wrap-ansi@8.1.0",
      "",
      {
        "dependencies": {
          "ansi-styles": "^6.1.0",
          "string-width": "^5.0.1",
          "strip-ansi": "^7.0.1",
        },
      },
      "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==",
    ],

    "@rollup/pluginutils/estree-walker": [
      "estree-walker@2.0.2",
      "",
      {},
      "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==",
    ],

    "@types/sax/@types/node": [
      "@types/node@22.10.5",
      "",
      { "dependencies": { "undici-types": "~6.20.0" } },
      "sha512-F8Q+SeGimwOo86fiovQh8qiXfFEh2/ocYv7tU5pJ3EXMSSxk1Joj5wefpFK2fHTf/N6HKGSxIDBT9f3gCxXPkQ==",
    ],

    "ansi-align/string-width": [
      "string-width@4.2.3",
      "",
      {
        "dependencies": {
          "emoji-regex": "^8.0.0",
          "is-fullwidth-code-point": "^3.0.0",
          "strip-ansi": "^6.0.1",
        },
      },
      "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
    ],

    "anymatch/picomatch": [
      "picomatch@2.3.1",
      "",
      {},
      "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
    ],

    "boxen/string-width": [
      "string-width@7.2.0",
      "",
      {
        "dependencies": {
          "emoji-regex": "^10.3.0",
          "get-east-asian-width": "^1.0.0",
          "strip-ansi": "^7.1.0",
        },
      },
      "sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==",
    ],

    "cliui/string-width": [
      "string-width@4.2.3",
      "",
      {
        "dependencies": {
          "emoji-regex": "^8.0.0",
          "is-fullwidth-code-point": "^3.0.0",
          "strip-ansi": "^6.0.1",
        },
      },
      "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
    ],

    "cliui/wrap-ansi": [
      "wrap-ansi@7.0.0",
      "",
      {
        "dependencies": {
          "ansi-styles": "^4.0.0",
          "string-width": "^4.1.0",
          "strip-ansi": "^6.0.0",
        },
      },
      "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
    ],

    "fast-glob/glob-parent": [
      "glob-parent@5.1.2",
      "",
      { "dependencies": { "is-glob": "^4.0.1" } },
      "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
    ],

    "load-yaml-file/js-yaml": [
      "js-yaml@3.14.1",
      "",
      {
        "dependencies": { "argparse": "^1.0.7", "esprima": "^4.0.0" },
        "bin": "bin/js-yaml.js",
      },
      "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==",
    ],

    "micromatch/picomatch": [
      "picomatch@2.3.1",
      "",
      {},
      "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
    ],

    "p-locate/p-limit": [
      "p-limit@2.3.0",
      "",
      { "dependencies": { "p-try": "^2.0.0" } },
      "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==",
    ],

    "parse-entities/@types/unist": [
      "@types/unist@2.0.11",
      "",
      {},
      "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==",
    ],

    "postcss-nested/postcss-selector-parser": [
      "postcss-selector-parser@6.1.2",
      "",
      { "dependencies": { "cssesc": "^3.0.0", "util-deprecate": "^1.0.2" } },
      "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==",
    ],

    "prompts/kleur": [
      "kleur@3.0.3",
      "",
      {},
      "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==",
    ],

    "read-cache/pify": [
      "pify@2.3.0",
      "",
      {},
      "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==",
    ],

    "string-width/strip-ansi": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "string-width-cjs/emoji-regex": [
      "emoji-regex@8.0.0",
      "",
      {},
      "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
    ],

    "string-width-cjs/strip-ansi": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "strip-ansi-cjs/ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "tailwindcss/chokidar": [
      "chokidar@3.6.0",
      "",
      {
        "dependencies": {
          "anymatch": "~3.1.2",
          "braces": "~3.0.2",
          "glob-parent": "~5.1.2",
          "is-binary-path": "~2.1.0",
          "is-glob": "~4.0.1",
          "normalize-path": "~3.0.0",
          "readdirp": "~3.6.0",
        },
        "optionalDependencies": { "fsevents": "~2.3.2" },
      },
      "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",
    ],

    "tailwindcss/postcss-selector-parser": [
      "postcss-selector-parser@6.1.2",
      "",
      { "dependencies": { "cssesc": "^3.0.0", "util-deprecate": "^1.0.2" } },
      "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==",
    ],

    "unstorage/chokidar": [
      "chokidar@3.6.0",
      "",
      {
        "dependencies": {
          "anymatch": "~3.1.2",
          "braces": "~3.0.2",
          "glob-parent": "~5.1.2",
          "is-binary-path": "~2.1.0",
          "is-glob": "~4.0.1",
          "normalize-path": "~3.0.0",
          "readdirp": "~3.6.0",
        },
        "optionalDependencies": { "fsevents": "~2.3.2" },
      },
      "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",
    ],

    "vite/@types/node": [
      "@types/node@22.10.5",
      "",
      { "dependencies": { "undici-types": "~6.20.0" } },
      "sha512-F8Q+SeGimwOo86fiovQh8qiXfFEh2/ocYv7tU5pJ3EXMSSxk1Joj5wefpFK2fHTf/N6HKGSxIDBT9f3gCxXPkQ==",
    ],

    "vscode-json-languageservice/jsonc-parser": [
      "jsonc-parser@3.3.1",
      "",
      {},
      "sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==",
    ],

    "widest-line/string-width": [
      "string-width@7.2.0",
      "",
      {
        "dependencies": {
          "emoji-regex": "^10.3.0",
          "get-east-asian-width": "^1.0.0",
          "strip-ansi": "^7.1.0",
        },
      },
      "sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==",
    ],

    "wrap-ansi/string-width": [
      "string-width@7.2.0",
      "",
      {
        "dependencies": {
          "emoji-regex": "^10.3.0",
          "get-east-asian-width": "^1.0.0",
          "strip-ansi": "^7.1.0",
        },
      },
      "sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==",
    ],

    "wrap-ansi/strip-ansi": [
      "strip-ansi@7.1.0",
      "",
      { "dependencies": { "ansi-regex": "^6.0.1" } },
      "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
    ],

    "wrap-ansi-cjs/ansi-styles": [
      "ansi-styles@4.3.0",
      "",
      { "dependencies": { "color-convert": "^2.0.1" } },
      "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
    ],

    "wrap-ansi-cjs/string-width": [
      "string-width@4.2.3",
      "",
      {
        "dependencies": {
          "emoji-regex": "^8.0.0",
          "is-fullwidth-code-point": "^3.0.0",
          "strip-ansi": "^6.0.1",
        },
      },
      "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
    ],

    "wrap-ansi-cjs/strip-ansi": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "yaml-language-server/prettier": [
      "prettier@2.8.7",
      "",
      { "bin": "bin-prettier.js" },
      "sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw==",
    ],

    "yaml-language-server/request-light": [
      "request-light@0.5.8",
      "",
      {},
      "sha512-3Zjgh+8b5fhRJBQZoy+zbVKpAQGLyka0MPgW3zruTF4dFFJ8Fqcfu9YsAvi/rvdcaTeWG3MkbZv4WKxAn/84Lg==",
    ],

    "yaml-language-server/vscode-languageserver": [
      "vscode-languageserver@7.0.0",
      "",
      {
        "dependencies": { "vscode-languageserver-protocol": "3.16.0" },
        "bin": {
          "installServerIntoExtension": "bin/installServerIntoExtension",
        },
      },
      "sha512-60HTx5ID+fLRcgdHfmz0LDZAXYEV68fzwG0JWwEPBode9NuMYTIxuYXPg4ngO8i8+Ou0lM7y6GzaYWbiDL0drw==",
    ],

    "yaml-language-server/vscode-languageserver-types": [
      "vscode-languageserver-types@3.16.0",
      "",
      {},
      "sha512-k8luDIWJWyenLc5ToFQQMaSrqCHiLwyKPHKPQZ5zz21vM+vIVUSvsRpcbiECH4WR88K2XZqc4ScRcZ7nk/jbeA==",
    ],

    "yaml-language-server/yaml": [
      "yaml@2.2.2",
      "",
      {},
      "sha512-CBKFWExMn46Foo4cldiChEzn7S7SRV+wqiluAb6xmueD/fGyRHIhX8m14vVGgeFWjN540nKCNVj6P21eQjgTuA==",
    ],

    "@isaacs/cliui/string-width/emoji-regex": [
      "emoji-regex@9.2.2",
      "",
      {},
      "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==",
    ],

    "@isaacs/cliui/strip-ansi/ansi-regex": [
      "ansi-regex@6.1.0",
      "",
      {},
      "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
    ],

    "ansi-align/string-width/emoji-regex": [
      "emoji-regex@8.0.0",
      "",
      {},
      "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
    ],

    "ansi-align/string-width/strip-ansi": [
      "strip-ansi@6.0.1",
      "",
      { "dependencies": { "ansi-regex": "^5.0.1" } },
      "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
    ],

    "boxen/string-width/emoji-regex": [
      "emoji-regex@10.4.0",
      "",
      {},
      "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==",
    ],

    "boxen/string-width/strip-ansi": [
      "strip-ansi@7.1.0",
      "",
      { "dependencies": { "ansi-regex": "^6.0.1" } },
      "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
    ],

    "cliui/string-width/emoji-regex": [
      "emoji-regex@8.0.0",
      "",
      {},
      "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
    ],

    "cliui/wrap-ansi/ansi-styles": [
      "ansi-styles@4.3.0",
      "",
      { "dependencies": { "color-convert": "^2.0.1" } },
      "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
    ],

    "load-yaml-file/js-yaml/argparse": [
      "argparse@1.0.10",
      "",
      { "dependencies": { "sprintf-js": "~1.0.2" } },
      "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==",
    ],

    "string-width-cjs/strip-ansi/ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "string-width/strip-ansi/ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "tailwindcss/chokidar/glob-parent": [
      "glob-parent@5.1.2",
      "",
      { "dependencies": { "is-glob": "^4.0.1" } },
      "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
    ],

    "tailwindcss/chokidar/readdirp": [
      "readdirp@3.6.0",
      "",
      { "dependencies": { "picomatch": "^2.2.1" } },
      "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",
    ],

    "unstorage/chokidar/glob-parent": [
      "glob-parent@5.1.2",
      "",
      { "dependencies": { "is-glob": "^4.0.1" } },
      "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
    ],

    "unstorage/chokidar/readdirp": [
      "readdirp@3.6.0",
      "",
      { "dependencies": { "picomatch": "^2.2.1" } },
      "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",
    ],

    "widest-line/string-width/emoji-regex": [
      "emoji-regex@10.4.0",
      "",
      {},
      "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==",
    ],

    "widest-line/string-width/strip-ansi": [
      "strip-ansi@7.1.0",
      "",
      { "dependencies": { "ansi-regex": "^6.0.1" } },
      "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
    ],

    "wrap-ansi-cjs/string-width/emoji-regex": [
      "emoji-regex@8.0.0",
      "",
      {},
      "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
    ],

    "wrap-ansi-cjs/strip-ansi/ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "wrap-ansi/string-width/emoji-regex": [
      "emoji-regex@10.4.0",
      "",
      {},
      "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==",
    ],

    "wrap-ansi/strip-ansi/ansi-regex": [
      "ansi-regex@6.1.0",
      "",
      {},
      "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
    ],

    "yaml-language-server/vscode-languageserver/vscode-languageserver-protocol": [
      "vscode-languageserver-protocol@3.16.0",
      "",
      {
        "dependencies": {
          "vscode-jsonrpc": "6.0.0",
          "vscode-languageserver-types": "3.16.0",
        },
      },
      "sha512-sdeUoAawceQdgIfTI+sdcwkiK2KU+2cbEYA0agzM2uqaUy2UpnnGHtWTHVEtS0ES4zHU0eMFRGN+oQgDxlD66A==",
    ],

    "ansi-align/string-width/strip-ansi/ansi-regex": [
      "ansi-regex@5.0.1",
      "",
      {},
      "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
    ],

    "boxen/string-width/strip-ansi/ansi-regex": [
      "ansi-regex@6.1.0",
      "",
      {},
      "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
    ],

    "tailwindcss/chokidar/readdirp/picomatch": [
      "picomatch@2.3.1",
      "",
      {},
      "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
    ],

    "unstorage/chokidar/readdirp/picomatch": [
      "picomatch@2.3.1",
      "",
      {},
      "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
    ],

    "widest-line/string-width/strip-ansi/ansi-regex": [
      "ansi-regex@6.1.0",
      "",
      {},
      "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
    ],

    "yaml-language-server/vscode-languageserver/vscode-languageserver-protocol/vscode-jsonrpc": [
      "vscode-jsonrpc@6.0.0",
      "",
      {},
      "sha512-wnJA4BnEjOSyFMvjZdpiOwhSq9uDoK8e/kpRJDTaMYzwlkrhG1fwDIZI94CLsLzlCK5cIbMMtFlJlfR57Lavmg==",
    ],
  },
}
