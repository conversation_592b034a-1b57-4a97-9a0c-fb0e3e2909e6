# <PERSON>on <PERSON>lli - Personal Portfolio

[![Portfolio Preview](./public/projects/valon/valon-banner.png)](https://valonmulolli.vercel.app)

<div align="center">

[Live Website](https://valonmulolli.vercel.app) | [Source Code](https://github.com/valonmulolli/valon)

</div>

## About Me

Hello! I'm <PERSON><PERSON>, a passionate developer and technology enthusiast. This is my personal portfolio and blog where I share my projects, tutorials, and thoughts on software development.

## Features

- **Portfolio** - Showcase of my projects and work
- **Blog** - Technical articles and tutorials
- **Responsive Design** - Works on all devices
- **Dark/Light Mode** - Easy on the eyes, day or night
- **Fast & SEO Optimized** - Built with Astro for optimal performance

## Tech Stack

- [Astro](https://astro.build) - The web framework for content-driven websites
- [Tailwind CSS](https://tailwindcss.com) - For styling
- [MDX](https://mdxjs.com) - For rich content
- Dark/Light mode
- Built-in search functionality

## Getting Started

1. Clone the repository

   ```bash
   git clone https://github.com/valonmulolli/valon.git
   cd valon
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Start the development server

   ```bash
   npm run dev
   ```

4. Open [http://localhost:4321](http://localhost:4321) in your browser

## License

This project is open source and available under the [MIT License](LICENSE).

## Connect with Me

- [GitHub](https://github.com/valonmulolli)
- [Twitter](https://twitter.com/valonmulolli)
- [LinkedIn](https://linkedin.com/in/valonmulolli)
- [Email](mailto:<EMAIL>)
