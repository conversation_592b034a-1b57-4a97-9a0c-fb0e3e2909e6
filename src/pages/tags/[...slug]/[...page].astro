---
import BlogCardButton from "@components/BlogCardButton.astro";
import PageNavigation from "@components/PageNavigation.astro";
import ProjectCardButton from "@components/ProjectCardButton.astro";
import RootPageIndex from "@components/RootPageIndex.astro";
import { getAllEntriesWithTags, resolvePath } from "@lib/utils";
import type { CollectionEntry } from "astro:content";

import BackToPrevious from "@components/BackToPrevious.astro";
import { TAGS, type CollectionName } from "@consts";

export async function getStaticPaths({ paginate }: { paginate: any }) {
  const { entries, tags } = await getAllEntriesWithTags();

  return tags.flatMap((tag) => {
    const tagEntries = entries.filter((post) => post.data.tags?.includes(tag));
    return paginate(tagEntries, {
      params: { slug: tag },
      pageSize: TAGS.pageSize,
    });
  });
}

const { slug } = Astro.params;
const { page } = Astro.props;

const entries = page.data;

const groupedEntries: {
  collection: string;
  entries: CollectionEntry<CollectionName>[];
}[] = [];

let currentCollection: CollectionName | undefined;

for (const entry of entries) {
  const collection = entry.collection;

  if (collection !== currentCollection) {
    currentCollection = collection;
    groupedEntries.push({
      collection,
      entries: [],
    });
  }

  groupedEntries[groupedEntries.length - 1].entries.push(entry);
}
---

<RootPageIndex title={`# ${slug}`}>
  <div class="animate">
    <BackToPrevious href={resolvePath("/tags", Astro.url.pathname)}
      >All tags</BackToPrevious
    >
  </div>
  {
    (
      <ul class="animate not-prose mb-12 flex flex-col gap-4">
        {groupedEntries.map((group) => (
          <li>
            <div class="mb-4 text-sm text-gray-500">{group.collection}:</div>
            <ul class="flex flex-col gap-4">
              {group.entries.map((entry) => (
                <li>
                  {group.collection === "blog" ? (
                    <BlogCardButton entry={entry as CollectionEntry<"blog">} />
                  ) : undefined}

                  {group.collection === "projects" ? (
                    <ProjectCardButton
                      entry={entry as CollectionEntry<"projects">}
                    />
                  ) : undefined}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    )
  }
  <PageNavigation page={page} />
</RootPageIndex>
