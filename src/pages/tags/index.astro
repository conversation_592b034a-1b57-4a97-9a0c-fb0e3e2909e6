---
import { getAllEntriesWithTags } from "@lib/utils";
import RootPageIndex from "@components/RootPageIndex.astro";
import Tag from "@components/Tag.astro";

const { entries, tags } = await getAllEntriesWithTags();
---

<RootPageIndex title="Tags">
    {
        (
            <div class="animate flex flex-wrap gap-2">
                {tags.map((tag) => (
                    <Tag
                        tag={tag}
                        entriesCount={
                            entries.filter((post) =>
                                post.data.tags?.includes(tag),
                            ).length
                        }
                    />
                ))}
            </div>
        )
    }
</RootPageIndex>
