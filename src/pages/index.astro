---
import BlogCardButton from "@components/BlogCardButton.astro";
import Contact from "@components/Contact.astro";
import Container from "@components/Container.astro";
import ProjectCardButton from "@components/ProjectCardButton.astro";
import TextLink from "@components/TextLink.astro";
import TutorialCardButton from "@components/TutorialCardButton.astro";
import { GLOBAL, HOME, type CollectionName } from "@consts";
import Layout from "@layouts/Layout.astro";
import { getFilteredCollectionEntries, resolvePath } from "@lib/utils";
import type { CollectionEntry } from "astro:content";
import Bio from "../components/Bio.astro";

async function getCollectionEntries<T extends CollectionName>(
  collectionName: T,
  maxItems: number,
): Promise<CollectionEntry<T>[]> {
  const { entries } = await getFilteredCollectionEntries(collectionName);

  return entries.slice(0, maxItems);
}

const blog = await getCollectionEntries("blog", HOME.blogEntries ?? 0);

const projects = await getCollectionEntries(
  "projects",
  HOME.projectEntries ?? 0,
);

const tutorials = await getCollectionEntries(
  "tutorials",
  HOME.tutorialEntries ?? 0,
);
---

<Layout>
  <Container>
    <aside data-pagefind-ignore>
      <div class="animate mb-8 flex items-center justify-center">
        <img
          src={resolvePath(GLOBAL.authorPhotoSrc, Astro.url.pathname)}
          alt={GLOBAL.author}
          class="h-48 w-48 rounded-full"
        />
      </div>
      <h1 class="animate text-3xl font-semibold text-black dark:text-white">
        Hi, I'm {GLOBAL.author}
      </h1>
      <div class="space-y-16">
        <section>
          <article class="animate space-y-4">
            <Bio />
            <h2 class="font-semibold text-black dark:text-white">
              Contact me:
            </h2>
            <Contact />
          </article>
        </section>

        {
          blog.length > 0 && (
            <section class="animate space-y-6">
              <div class="flex flex-wrap items-center justify-between gap-y-2">
                <h2 class="font-semibold text-black dark:text-white">
                  Recent blog posts
                </h2>
                <TextLink href="blog">all blog posts </TextLink>
              </div>
              <ul class="not-prose flex flex-col gap-4">
                {blog.map((post) => (
                  <li>
                    <BlogCardButton entry={post} />
                  </li>
                ))}
              </ul>
            </section>
          )
        }

        {
          tutorials.length > 0 && (
            <section class="animate space-y-6">
              <div class="flex flex-wrap items-center justify-between gap-y-2">
                <h2 class="font-semibold text-black dark:text-white">
                  Recent tutorials
                </h2>
                <TextLink href="tutorials">all tutorials </TextLink>
              </div>
              <ul class="not-prose flex flex-col gap-4">
                {tutorials.map((tutorial) => (
                  <li>
                    <TutorialCardButton entry={tutorial} />
                  </li>
                ))}
              </ul>
            </section>
          )
        }
        {
          projects.length > 0 && (
            <section class="animate space-y-6">
              <div class="flex flex-wrap items-center justify-between gap-y-2">
                <h2 class="font-semibold text-black dark:text-white">
                  Recent projects
                </h2>
                <TextLink href="projects">all projects </TextLink>
              </div>
              <ul class="not-prose flex flex-col gap-4">
                {projects.map((project) => (
                  <li>
                    <ProjectCardButton entry={project} />
                  </li>
                ))}
              </ul>
            </section>
          )
        }
      </div>
    </aside>
  </Container>
</Layout>
