---
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import { getFilteredCollectionEntries, resolvePath } from "@lib/utils";
import BackToPrevious from "@components/BackToPrevious.astro";
import TableOfContents from "@components/TableOfContents.astro";
import EntryHeader from "@components/EntryHeader.astro";

export async function getStaticPaths() {
  const { entries } = await getFilteredCollectionEntries("projects");
  return entries.map((post) => ({
    params: { slug: post.slug },
    props: post,
  }));
}

const entry = Astro.props;
const { Content, headings } = await entry.render();
---

<Layout
  title={entry.data.title}
  description={entry.data.description}
  ogImage={entry.data.ogImage}
>
  <Container>
    <div class="animate">
      <BackToPrevious href={resolvePath("/projects", Astro.url.pathname)}
        >All projects</BackToPrevious
      >
    </div>

    <EntryHeader {...entry.data} body={entry.body} />
    {headings?.length ? <TableOfContents headings={headings} /> : undefined}
    <article class="animate">
      <hr />
      <Content />
    </article>
  </Container>
</Layout>
