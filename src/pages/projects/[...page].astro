---
import { getFilteredCollectionEntries } from "@lib/utils";
import RootPageIndex from "@components/RootPageIndex.astro";
import { PROJECTS } from "@consts";
import PageNavigation from "@components/PageNavigation.astro";
import ProjectCardButton from "@components/ProjectCardButton.astro";

export async function getStaticPaths({ paginate }: { paginate: any }) {
  const { entries } = await getFilteredCollectionEntries("projects");
  const posts = entries.map((post) => ({
    params: { slug: post.slug },
    props: post,
  }));

  return paginate(posts, { pageSize: PROJECTS.pageSize });
}

const { page } = Astro.props as {
  page: { data: { props: any }[]; currentPage: number; lastPage: number };
};

const entries = page.data.map((pageEntry) => ({
  ...pageEntry.props,
}));
---

<RootPageIndex title="Projects">
  {
    (
      <ul class="animate not-prose mb-12 flex flex-col gap-4">
        {entries.map((entry) => (
          <li>
            <ProjectCardButton entry={entry} />
          </li>
        ))}
      </ul>
    )
  }

  <PageNavigation page={page} />
</RootPageIndex>
