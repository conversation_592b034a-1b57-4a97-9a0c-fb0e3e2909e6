---
import PageNavigation from "@components/PageNavigation.astro";
import RootPageIndex from "@components/RootPageIndex.astro";
import TutorialCardButton from "@components/TutorialCardButton.astro";
import { TUTORIALS } from "@consts";
import { getFilteredCollectionEntries } from "@lib/utils";

export async function getStaticPaths({ paginate }: { paginate: any }) {
  const { entries } = await getFilteredCollectionEntries("tutorials");
  const posts = entries.map((post) => ({
    params: { slug: post.slug },
    props: post,
  }));

  return paginate(posts, { pageSize: TUTORIALS.pageSize });
}

const { page } = Astro.props as {
  page: { data: { props: any }[]; currentPage: number; lastPage: number };
};

const entries = page.data.map((pageEntry) => ({
  ...pageEntry.props,
}));
---

<RootPageIndex title="Tutorials">
  {
    (
      <ul class="animate not-prose mb-12 flex flex-col gap-4">
        {entries.map((entry) => (
          <li>
            <TutorialCardButton entry={entry} />
          </li>
        ))}
      </ul>
    )
  }

  <PageNavigation page={page} />
</RootPageIndex>
