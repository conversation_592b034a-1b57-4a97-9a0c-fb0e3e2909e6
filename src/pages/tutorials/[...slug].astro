---
import BackToPrevious from "@components/BackToPrevious.astro";
import Container from "@components/Container.astro";
import EntryHeader from "@components/EntryHeader.astro";
import TableOfContents from "@components/TableOfContents.astro";
import Layout from "@layouts/Layout.astro";
import { getFilteredCollectionEntries, resolvePath } from "@lib/utils";

export async function getStaticPaths() {
  const { entries } = await getFilteredCollectionEntries("tutorials");
  return entries.map((post) => ({
    params: { slug: post.slug },
    props: post,
  }));
}

const entry = Astro.props;
const { Content, headings } = await entry.render();

// Format difficulty with an appropriate icon/color
const getDifficultyLabel = (difficulty: string | undefined) => {
  if (!difficulty) return "";
  
  const icons = {
    beginner: "🟢",
    intermediate: "🟠",
    advanced: "🔴"
  };
  
  return `${icons[difficulty as keyof typeof icons]} ${difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}`;
};

// Get meta information
const metaInfo = [];
if (entry.data.difficulty) {
  metaInfo.push(getDifficultyLabel(entry.data.difficulty));
}
if (entry.data.duration) {
  metaInfo.push(`⏱️ ${entry.data.duration}`);
}
const metaInfoText = metaInfo.join(" · ");
---

<Layout
  title={entry.data.title}
  description={entry.data.description}
  ogImage={entry.data.ogImage}
>
  <Container>
    <div class="animate">
      <BackToPrevious href={resolvePath("/tutorials", Astro.url.pathname)}
        >All tutorials</BackToPrevious
      >
    </div>
    <EntryHeader {...entry.data} body={entry.body} />
    
    {metaInfoText && (
      <div class="animate mb-6 text-lg">
        <p>{metaInfoText}</p>
      </div>
    )}
    
    {headings?.length ? <TableOfContents headings={headings} /> : undefined}
    <article class="animate">
      <hr />
      <Content />
    </article>
  </Container>
</Layout>
