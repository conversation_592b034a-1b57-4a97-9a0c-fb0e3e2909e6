---
import BackToPrevious from "@components/BackToPrevious.astro";
import BlogLicenceInfo from "@components/BlogLicenseInfo.astro";
import Container from "@components/Container.astro";
import EntryHeader from "@components/EntryHeader.astro";
import Giscus from "@components/Giscus.astro";
import PostNavigation from "@components/PostNavigation.astro";
import TableOfContents from "@components/TableOfContents.astro";
import Layout from "@layouts/Layout.astro";
import {
  getFilteredCollectionEntries,
  getNavigationEntries,
  resolvePath,
} from "@lib/utils";
import BlogAuthor from "../../components/BlogAuthor.astro";

export async function getStaticPaths() {
  const { entries } = await getFilteredCollectionEntries("blog");
  return entries.map((post) => ({
    params: { slug: post.slug },
    props: post,
  }));
}
const { prevPost, nextPost } = await getNavigationEntries(
  "blog",
  Astro.params.slug
);

const entry = Astro.props;
const { Content, headings } = await entry.render();
---

<Layout
  title={entry.data.title}
  {...entry.data.description && { description: entry.data.description }}
  {...entry.data.ogImage && { ogImage: entry.data.ogImage }}
>
  <Container>
    <div class="animate">
      <BackToPrevious href={resolvePath("/blog", Astro.url.pathname)}>
        All blog posts
      </BackToPrevious>
    </div>
    <EntryHeader {...entry.data} body={entry.body} />
    <div data-pagefind-meta="title" data-pagefind-value={entry.data.title}>
    </div>
    <div
      data-pagefind-meta="description"
      data-pagefind-value={entry.data.description}
    >
    </div>
    {
      entry.data.tags && (
        <div
          data-pagefind-meta="tags"
          data-pagefind-value={entry.data.tags.join(", ")}
        />
      )
    }

    {headings.length > 0 && <TableOfContents headings={headings} />}
    <article class="animate" data-pagefind-body>
      <Content />
      <hr class="my-4" />
      <div class="my-12"><BlogAuthor /></div>

      <hr class="my-4" />
      <div class="my-4">
        <PostNavigation prevPost={prevPost} nextPost={nextPost} />
      </div>
      <BlogLicenceInfo />
      <div class="mt-8">
        <Giscus />
      </div>
    </article>
  </Container>
</Layout>
