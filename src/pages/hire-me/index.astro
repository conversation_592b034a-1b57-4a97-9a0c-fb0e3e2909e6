---
import ArrowCardButton from "@components/ArrowCardButton.astro";
import RootPageIndex from "@components/RootPageIndex.astro";

import { CONTACT } from "@consts";
import { resolvePath } from "@lib/utils";

const linkedInContact = CONTACT.find((contact) => contact.type === "LinkedIn");
---

<RootPageIndex title="Hire me" searchable>
  <div class="animate mb-4">
    <p class="mb-1">The PDF resume:</p>
    <section>
      <ArrowCardButton
        href={resolvePath("/hire-me/valon_resume.pdf", Astro.url.pathname)}
        text="Download PDF"
        openInNewTab
      />
    </section>
  </div>
  <div class="animate mb-4">
    <p class="mb-1">The TXT resume:</p>
    <section>
      <ArrowCardButton
        href={resolvePath("/hire-me/valon_resume.txt", Astro.url.pathname)}
        text="Download TXT"
        openInNewTab
      />
    </section>
  </div>

  {
    linkedInContact && (
      <div class="animate mb-4">
        <p class="mb-1">Or you can check my LinkedIn profile:</p>
        <section>
          <ArrowCardButton
            href={linkedInContact.href}
            text="Navigate to LinkedIn"
            openInNewTab
          />
        </section>
      </div>
    )
  }
</RootPageIndex>
