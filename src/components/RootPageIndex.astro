---
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";

type Props = {
  title: string;
  searchable?: boolean;
};

const { title, searchable } = Astro.props;
---

<Layout title={title}>
  <Container>
    <aside {...!searchable ? { "data-pagefind-ignore": true } : {}}>
      <div class="space-y-10">
        <h1 class="animate text-3xl font-semibold text-black dark:text-white">
          {title}
        </h1>
        <div class="space-y-4">
          <slot />
        </div>
      </div>
    </aside>
  </Container>
</Layout>
