---
import type { CollectionEntry } from "astro:content";
import ArrowCardButton from "./ArrowCardButton.astro";
import { formatDateWithLastUpdateDate, resolvePath } from "@lib/utils";

type Props = {
  entry: CollectionEntry<"tutorials">;
};

const { entry } = Astro.props;

// Format difficulty with an appropriate icon/color
const getDifficultyLabel = (difficulty: string | undefined) => {
  if (!difficulty) return "";
  
  const icons = {
    beginner: "🟢",
    intermediate: "🟠",
    advanced: "🔴"
  };
  
  return `${icons[difficulty as keyof typeof icons]} ${difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}`;
};

// Combine difficulty and duration if available
const getMetaInfo = () => {
  const parts = [];
  
  if (entry.data.difficulty) {
    parts.push(getDifficultyLabel(entry.data.difficulty));
  }
  
  if (entry.data.duration) {
    parts.push(`⏱️ ${entry.data.duration}`);
  }
  
  return parts.join(" · ");
};

const metaInfo = getMetaInfo();
---

<ArrowCardButton
  href={resolvePath(`/${entry.collection}/${entry.slug}`, Astro.url.pathname)}
  textAbove={formatDateWithLastUpdateDate(
    entry.data.date,
    entry.data.lastUpdateDate,
  )}
  text={entry.data.title}
  textBelow={entry.data.description || metaInfo ? `${entry.data.description || ""} ${entry.data.description && metaInfo ? "· " : ""}${metaInfo}` : undefined}
/>
