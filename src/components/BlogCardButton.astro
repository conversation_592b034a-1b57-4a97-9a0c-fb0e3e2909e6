---
import type { CollectionEntry } from "astro:content";
import ArrowCardButton from "./ArrowCardButton.astro";
import { formatDateWithLastUpdateDate, resolvePath } from "@lib/utils";

type Props = {
  entry: CollectionEntry<"blog">;
};

const { entry } = Astro.props;
---

<ArrowCardButton
  href={resolvePath(`/${entry.collection}/${entry.slug}`, Astro.url.pathname)}
  textAbove={formatDateWithLastUpdateDate(
    entry.data.date,
    entry.data.lastUpdateDate,
  )}
  text={entry.data.title}
  textBelow={entry.data.description}
/>
