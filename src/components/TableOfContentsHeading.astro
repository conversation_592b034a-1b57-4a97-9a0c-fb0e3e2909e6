---
import type { Heading } from "./TableOfContents.astro";
import TextLink from "./TextLink.astro";

// https://kld.dev/building-table-of-contents/

const { heading } = Astro.props;
---

<li class="list-inside list-disc px-6 py-1.5 text-sm">
  <TextLink href={"#" + heading.slug} underline>
    {heading.text}
  </TextLink>
  {
    heading.subheadings.length > 0 && (
      <ul class="translate-x-3">
        {heading.subheadings.map((subheading: Heading) => (
          <Astro.self heading={subheading} />
        ))}
      </ul>
    )
  }
</li>
