---
// @ts-ignore
import TextLink from "../components/TextLink.astro";
// @ts-ignore
import { resolvePath } from "../lib/utils";

type Props = {
  small?: boolean;
};

const { small } = Astro.props;
---

<div style={small ? "font-size: 0.875rem" : ""}>
  <p>
    Welcome to my personal blog and portfolio. I'm <PERSON><PERSON>
  </p>

  <p>
    This site is built with{" "}
    <TextLink href="https://astro.build">Astro</TextLink>, a modern static site
    generator that allows for fast, content-focused websites.
  </p>

  <p>
    I'm passionate about technology and development. This blog serves as a
    platform for me to share my thoughts, projects, and experiences in the tech
    world.
  </p>

  <p>
    Feel free to explore my{" "}
    <TextLink href={resolvePath("/blog", Astro.url.pathname)}>
      blog posts
    </TextLink>{" "}
    and{" "}
    <TextLink href={resolvePath("/projects", Astro.url.pathname)}>
      projects
    </TextLink>
    . You can also find me on{" "}
    <TextLink href="https://github.com/valonmulolli">GitHub</TextLink>.
  </p>

  <p>Thanks for visiting!</p>
</div> 