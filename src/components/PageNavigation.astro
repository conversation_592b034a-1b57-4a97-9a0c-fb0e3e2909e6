---
const { page } = Astro.props;
---

{
  page.lastPage !== 1 && (
    <section class="animate text-center">
      <div class="mb-4">
        this is page {page.currentPage} of {page.lastPage}
      </div>

      <div class="grid grid-cols-2 gap-1.5 sm:gap-3">
        {page?.url.prev ? (
          <a
            href={page.url.prev}
            class="group relative flex flex-nowrap rounded-lg border border-black/15 px-4 py-3 pl-10 no-underline transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              class="absolute left-2 top-1/2 size-5 -translate-y-1/2 fill-none stroke-current stroke-2"
            >
              <line
                x1="5"
                y1="12"
                x2="19"
                y2="12"
                class="translate-x-3 scale-x-0 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-hover:scale-x-100 group-focus-visible:translate-x-0 group-focus-visible:scale-x-100"
              />
              <polyline
                points="12 5 5 12 12 19"
                class="translate-x-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-focus-visible:translate-x-0"
              />
            </svg>
            <div class="flex items-center text-sm">Previous page</div>
          </a>
        ) : (
          <div class="invisible" />
        )}

        {page?.url.next ? (
          <a
            href={page.url.next}
            class="group relative flex flex-grow flex-row-reverse flex-nowrap rounded-lg border border-black/15 px-4 py-4 pr-10 no-underline transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              class="absolute right-2 top-1/2 size-5 -translate-y-1/2 fill-none stroke-current stroke-2"
            >
              <line
                x1="5"
                y1="12"
                x2="19"
                y2="12"
                class="translate-x-3 scale-x-0 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-hover:scale-x-100 group-focus-visible:translate-x-0 group-focus-visible:scale-x-100"
              />
              <polyline
                points="12 5 19 12 12 19"
                class="-translate-x-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-focus-visible:translate-x-0"
              />
            </svg>
            <div class="flex items-center text-sm">Next page</div>
          </a>
        ) : (
          <div class="invisible" />
        )}
      </div>
    </section>
  )
}
