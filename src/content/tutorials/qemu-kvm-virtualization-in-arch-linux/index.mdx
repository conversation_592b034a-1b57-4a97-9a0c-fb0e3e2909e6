---
title: "QEMU/KVM Virtualization in Arch Linux: A Complete Guide"
description: "Learn how to set up, configure, and optimize QEMU/KVM virtual machines on Arch Linux"
date: "2024-06-24"
lastUpdateDate: "2024-06-24"
ogImage: "/tutorials/qemu-kvm-virtualization-in-arch-linux/og-image.png"
tags:
  - arch-linux
  - virtualization
  - qemu
  - kvm
  - linux
---

import Callout from "@components/Callout.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";

# QEMU/KVM Virtualization in Arch Linux: A Complete Guide

Virtualization allows you to run multiple operating systems simultaneously on a single physical machine. QEMU with KVM is a powerful, open-source virtualization solution that provides near-native performance for virtual machines. This tutorial will guide you through setting up and using QEMU/KVM on Arch Linux.

## What is QEMU/KVM?

**QEMU** (Quick Emulator) is a generic machine emulator and virtualizer that can run operating systems and programs for one machine on a different machine.

**KVM** (Kernel-based Virtual Machine) is a Linux kernel module that allows the kernel to function as a hypervisor, enabling a host computer to run multiple isolated virtual environments called virtual machines (VMs).

When used together, QEMU provides the emulation layer while KVM provides hardware acceleration, resulting in near-native performance for virtual machines.

## Prerequisites

Before we begin, ensure your system meets these requirements:

1. **CPU with virtualization support**: Your processor must support hardware virtualization (Intel VT-x or AMD-V).
2. **Virtualization enabled in BIOS/UEFI**: Hardware virtualization must be enabled in your system's BIOS/UEFI settings.
3. **Arch Linux**: This guide is specifically for Arch Linux, though the concepts apply to most Linux distributions.
4. **Root or sudo access**: You'll need administrative privileges to install packages and configure the system.

## Checking for Virtualization Support

First, let's verify that your CPU supports virtualization and that it's enabled:

```bash
LC_ALL=C lscpu | grep Virtualization
```

For Intel CPUs, you should see "VT-x" in the output. For AMD CPUs, you should see "AMD-V".

You can also check if KVM can be used:

```bash
zgrep CONFIG_KVM /proc/config.gz
```

You should see `CONFIG_KVM=y` and either `CONFIG_KVM_INTEL=y` or `CONFIG_KVM_AMD=y` depending on your CPU.

<Callout type="info">
  If virtualization is not enabled, you'll need to restart your computer and enable it in your BIOS/UEFI settings. The option is typically found under CPU settings and might be called "Virtualization Technology," "VT-x," "AMD-V," or similar.
</Callout>

## Installing QEMU/KVM and Related Packages

Let's install the necessary packages:

```bash
sudo pacman -S qemu-full virt-manager libvirt edk2-ovmf virt-viewer dnsmasq bridge-utils
```

Here's what each package provides:

- `qemu-full`: The QEMU emulator with all supported targets
- `virt-manager`: A graphical user interface for managing virtual machines
- `libvirt`: The virtualization API library
- `edk2-ovmf`: UEFI firmware for virtual machines
- `virt-viewer`: A lightweight UI for displaying VM consoles
- `dnsmasq`: Provides DNS and DHCP services for virtual networks
- `bridge-utils`: Utilities for configuring network bridges

## Setting Up libvirt

After installation, we need to start and enable the libvirt daemon:

```bash
sudo systemctl enable libvirtd.service
sudo systemctl start libvirtd.service
```

To allow your user to manage virtual machines without using sudo, add your user to the `libvirt` group:

```bash
sudo usermod -aG libvirt $(whoami)
sudo usermod -aG kvm $(whoami)
```

<Callout type="warning">
  You'll need to log out and log back in for the group changes to take effect.
</Callout>

## Configuring Network for Virtual Machines

libvirt provides several network modes for virtual machines. The default network uses NAT (Network Address Translation) and is usually sufficient for most users.

To ensure the default network is started automatically:

```bash
sudo virsh net-autostart default
sudo virsh net-start default
```

If you want to check the status of the default network:

```bash
sudo virsh net-list --all
```

### Setting Up a Bridge Network (Optional)

If you want your VMs to appear as separate devices on your network, you can set up a bridge network. This is more advanced but gives your VMs direct access to your physical network.

1. Install the necessary packages:

```bash
sudo pacman -S netctl
```

2. Create a bridge configuration file:

```bash
sudo nano /etc/netctl/bridge
```

3. Add the following content (adjust according to your network):

```
Description="Bridge connection"
Interface=br0
Connection=bridge
BindsToInterfaces=(eth0)  # Replace with your network interface
IP=dhcp
## For static IP
#IP=static
#Address=('***********/24')
#Gateway='***********'
#DNS=('***********')
```

4. Stop your current network connection and start the bridge:

```bash
sudo netctl stop-all
sudo netctl start bridge
```

5. To make it start automatically on boot:

```bash
sudo netctl enable bridge
```

6. Create a new libvirt network that uses this bridge:

```bash
sudo virsh net-define /dev/stdin <<EOF
<network>
  <name>br0</name>
  <forward mode="bridge"/>
  <bridge name="br0"/>
</network>
EOF
```

7. Set it to autostart:

```bash
sudo virsh net-autostart br0
sudo virsh net-start br0
```

## Creating Your First Virtual Machine

Now that we have everything set up, let's create a virtual machine. We'll use `virt-manager` for this, as it provides a user-friendly interface.

1. Launch virt-manager:

```bash
virt-manager
```

2. Click on the "Create a new virtual machine" button (the computer icon with a plus sign).

3. Select "Local install media" and click "Forward".

4. Browse and select your ISO file, or enter the path to it. virt-manager will try to detect the operating system. Click "Forward".

5. Allocate memory (RAM) and CPUs for your VM. The defaults are usually fine for basic usage, but adjust according to your needs and system capabilities. Click "Forward".

6. Create a disk image for your VM. This will be the virtual hard drive. Set the size according to your needs. Click "Forward".

7. Give your VM a name and check the "Customize configuration before install" option if you want to make additional adjustments. Click "Finish".

8. If you chose to customize, you'll see various hardware settings you can adjust. When you're done, click "Begin Installation" in the top-left corner.

9. The VM will start, and you can proceed with the installation of your chosen operating system.

## Managing Virtual Machines from the Command Line

While virt-manager is convenient, you can also manage your VMs from the command line using `virsh`.

### Listing Virtual Machines

```bash
virsh list --all
```

### Starting a Virtual Machine

```bash
virsh start vm_name
```

### Stopping a Virtual Machine

```bash
virsh shutdown vm_name
```

### Forcefully Stopping a Virtual Machine

```bash
virsh destroy vm_name
```

<Callout type="warning">
  The `destroy` command forcefully stops a VM, similar to pulling the plug on a physical computer. Use it only when necessary.
</Callout>

### Removing a Virtual Machine

```bash
virsh undefine vm_name
```

To also delete the storage:

```bash
virsh undefine vm_name --remove-all-storage
```

### Connecting to a VM's Console

```bash
virsh console vm_name
```

To exit the console, press `Ctrl+]`.

## Advanced QEMU/KVM Configurations

### CPU Pinning

CPU pinning can improve performance by assigning specific virtual CPUs to specific physical CPU cores.

1. Get information about your CPU topology:

```bash
lscpu -e
```

2. Edit your VM's XML configuration:

```bash
virsh edit vm_name
```

3. Add or modify the `<vcpupin>` elements within the `<cputune>` section:

```xml
<cputune>
  <vcpupin vcpu="0" cpuset="1"/>
  <vcpupin vcpu="1" cpuset="3"/>
  <vcpupin vcpu="2" cpuset="5"/>
  <vcpupin vcpu="3" cpuset="7"/>
</cputune>
```

This example pins virtual CPUs 0-3 to physical CPU cores 1, 3, 5, and 7.

### Hugepages

Hugepages can improve memory performance by using larger page sizes.

1. Configure hugepages in your system:

```bash
sudo nano /etc/sysctl.d/40-hugepages.conf
```

2. Add the following line (adjust the number based on your VM's memory needs):

```
vm.nr_hugepages = 2048
```

This allocates 2048 hugepages of 2MB each (4GB total).

3. Apply the changes:

```bash
sudo sysctl -p /etc/sysctl.d/40-hugepages.conf
```

4. Edit your VM's XML configuration:

```bash
virsh edit vm_name
```

5. Add or modify the `<memoryBacking>` section:

```xml
<memoryBacking>
  <hugepages/>
</memoryBacking>
```

### PCI Passthrough

PCI passthrough allows a VM to directly access physical hardware, which is useful for GPUs, USB controllers, and other devices.

1. Enable IOMMU in your bootloader. Add the following kernel parameters to your bootloader configuration:

For Intel CPUs:
```
intel_iommu=on
```

For AMD CPUs:
```
amd_iommu=on
```

2. Identify the PCI device you want to pass through:

```bash
lspci -nnv
```

3. Find the device's IOMMU group:

```bash
shopt -s nullglob
for g in /sys/kernel/iommu_groups/*; do
    echo "IOMMU Group ${g##*/}:"
    for d in $g/devices/*; do
        echo -e "\t$(lspci -nns ${d##*/})"
    done
done
```

4. Add the device to the vfio-pci driver by creating a file:

```bash
sudo nano /etc/modprobe.d/vfio.conf
```

5. Add the following line, replacing the vendor and device IDs with those of your device:

```
options vfio-pci ids=10de:1234,10de:5678
```

6. Update your initramfs:

```bash
sudo mkinitcpio -P
```

7. Edit your VM's XML configuration to add the device:

```bash
virsh edit vm_name
```

8. Add the device in the `<devices>` section:

```xml
<hostdev mode='subsystem' type='pci' managed='yes'>
  <source>
    <address domain='0x0000' bus='0x01' slot='0x00' function='0x0'/>
  </source>
</hostdev>
```

Replace the address values with those of your device.

## Optimizing VM Performance

### Disk I/O Performance

1. Use virtio drivers for better disk performance:

```bash
virsh edit vm_name
```

2. Ensure your disk device uses the virtio driver:

```xml
<disk type='file' device='disk'>
  <driver name='qemu' type='qcow2' cache='none' io='native'/>
  <source file='/path/to/disk.qcow2'/>
  <target dev='vda' bus='virtio'/>
</disk>
```

### Network Performance

Similarly, use virtio for network devices:

```xml
<interface type='network'>
  <source network='default'/>
  <model type='virtio'/>
</interface>
```

### CPU Performance

Set the CPU mode to host-passthrough for best performance:

```xml
<cpu mode='host-passthrough' check='none'>
  <topology sockets='1' cores='4' threads='2'/>
</cpu>
```

Adjust the topology based on your physical CPU.

## Backing Up Virtual Machines

### Creating Snapshots

Snapshots allow you to save the state of a VM at a specific point in time:

```bash
virsh snapshot-create-as vm_name snapshot_name "Description of snapshot" --disk-only --atomic
```

### Listing Snapshots

```bash
virsh snapshot-list vm_name
```

### Reverting to a Snapshot

```bash
virsh snapshot-revert vm_name snapshot_name
```

### Backing Up VM Configuration

```bash
virsh dumpxml vm_name > vm_name_config.xml
```

### Backing Up VM Disk

```bash
sudo cp /var/lib/libvirt/images/vm_disk.qcow2 /backup/location/
```

## Troubleshooting Common Issues

### VM Won't Start

Check the libvirt logs:

```bash
sudo journalctl -u libvirtd
```

### No Network Connectivity in VM

Verify that the default network is running:

```bash
virsh net-list --all
```

If it's not active, start it:

```bash
virsh net-start default
```

### Poor Performance

1. Ensure you're using virtio drivers for disk and network.
2. Check if KVM acceleration is being used:

```bash
virsh dominfo vm_name | grep "CPU time"
```

If the CPU time is high even with minimal VM activity, KVM might not be working correctly.

3. Monitor VM performance:

```bash
virt-top
```

### Cannot Connect to VM Console

Make sure you have the right permissions:

```bash
sudo usermod -aG libvirt $(whoami)
```

Log out and log back in for the changes to take effect.

## Conclusion

QEMU/KVM provides a powerful virtualization solution for Arch Linux users. With the right configuration, you can achieve near-native performance for your virtual machines. This guide covered the basics of setting up and using QEMU/KVM, as well as some advanced configurations for optimizing performance.

Remember that virtualization is a complex topic, and there's always more to learn. The Arch Wiki is an excellent resource for more detailed information on specific aspects of QEMU/KVM virtualization.

<Callout type="tip">
  For Windows VMs, consider installing the virtio drivers for better performance. You can download them from the [Fedora Project](https://fedorapeople.org/groups/virt/virtio-win/direct-downloads/).
</Callout>

## Additional Resources

- [Arch Wiki: QEMU](https://wiki.archlinux.org/title/QEMU)
- [Arch Wiki: KVM](https://wiki.archlinux.org/title/KVM)
- [Arch Wiki: libvirt](https://wiki.archlinux.org/title/Libvirt)
- [QEMU Documentation](https://www.qemu.org/docs/master/)
- [libvirt Documentation](https://libvirt.org/docs.html)
