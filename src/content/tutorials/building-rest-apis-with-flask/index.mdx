---
title: "Building REST APIs with Flask: A Complete Guide"
description: "Learn how to create robust and scalable REST APIs using Python and Flask"
date: "2024-06-25"
lastUpdateDate: "2024-06-25"
difficulty: "intermediate"
duration: "60 minutes"
ogImage: "/tutorials/building-rest-apis-with-flask/og-image.png"
tags:
  - python
  - flask
  - api
  - web-development
---

import Callout from "@components/Callout.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";

---

## Introduction to REST APIs

REST (Representational State Transfer) APIs have become the standard for building web services. They allow different systems to communicate with each other over HTTP, making them essential for modern web and mobile applications.

In this tutorial, we'll learn how to build a robust REST API using Flask, a lightweight and flexible Python web framework. We'll create a complete API for managing a collection of books, including authentication, data validation, and proper error handling.

<Callout type="info">
  Flask is perfect for building APIs because of its simplicity and flexibility. It provides the essentials without imposing a rigid structure, allowing you to design your API exactly as you need it.
</Callout>

## Prerequisites

Before starting this tutorial, you should have:

- Intermediate knowledge of Python
- Basic understanding of HTTP and REST principles
- Familiarity with JSON
- Python 3.7+ installed on your computer

If you're new to Python, check out our [Getting Started with Python tutorial](/tutorials/getting-started-with-python) first.

## Step 1: Setting Up Your Environment

Let's start by setting up our development environment:

1. Create a new directory for your project:

```bash
mkdir flask-api-tutorial
cd flask-api-tutorial
```

2. Create a virtual environment and activate it:

```bash
# On Windows
python -m venv venv
venv\Scripts\activate

# On macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

3. Install the required packages:

```bash
pip install flask flask-sqlalchemy flask-jwt-extended marshmallow
```

These packages are:
- **Flask**: The web framework
- **Flask-SQLAlchemy**: ORM for database operations
- **Flask-JWT-Extended**: For handling authentication
- **Marshmallow**: For data serialization and validation

4. Create the basic project structure:

```
flask-api-tutorial/
├── app/
│   ├── __init__.py
│   ├── models.py
│   ├── routes.py
│   └── schemas.py
├── config.py
└── run.py
```

You can create this structure with the following commands:

```bash
mkdir app
touch app/__init__.py app/models.py app/routes.py app/schemas.py
touch config.py run.py
```

## Step 2: Configuring the Application

Let's start by setting up the configuration for our Flask application:

1. Open `config.py` and add the following:

```python
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
```

2. Now, let's create our Flask application in `app/__init__.py`:

```python
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from config import Config

# Initialize extensions
db = SQLAlchemy()
jwt = JWTManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    
    # Import and register blueprints
    from app.routes import api
    app.register_blueprint(api, url_prefix='/api')
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app
```

3. Create the entry point in `run.py`:

```python
from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
```

## Step 3: Creating the Data Models

Now, let's define our database models in `app/models.py`:

```python
from datetime import datetime
from app import db
from werkzeug.security import generate_password_hash, check_password_hash

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    books = db.relationship('Book', backref='author', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Book(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    pages = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    def __repr__(self):
        return f'<Book {self.title}>'
```

## Step 4: Creating Schemas for Serialization

We'll use Marshmallow to serialize our models to JSON and validate incoming data. Create the schemas in `app/schemas.py`:

```python
from marshmallow import Schema, fields, validate, ValidationError

class UserSchema(Schema):
    id = fields.Int(dump_only=True)
    username = fields.Str(required=True, validate=validate.Length(min=3, max=64))
    email = fields.Email(required=True)
    password = fields.Str(required=True, load_only=True, validate=validate.Length(min=6))
    books = fields.List(fields.Nested(lambda: BookSchema(exclude=('author',))), dump_only=True)

class BookSchema(Schema):
    id = fields.Int(dump_only=True)
    title = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    description = fields.Str()
    pages = fields.Int(validate=validate.Range(min=1))
    created_at = fields.DateTime(dump_only=True)
    user_id = fields.Int(dump_only=True)
    author = fields.Nested(UserSchema(only=('id', 'username')), dump_only=True)

# Initialize schemas
user_schema = UserSchema()
users_schema = UserSchema(many=True)
book_schema = BookSchema()
books_schema = BookSchema(many=True)
```

## Step 5: Creating API Routes

Now, let's create our API routes in `app/routes.py`:

```python
from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from marshmallow import ValidationError
from app import db
from app.models import User, Book
from app.schemas import user_schema, book_schema, books_schema

api = Blueprint('api', __name__)

# Error handler for validation errors
@api.errorhandler(ValidationError)
def handle_validation_error(error):
    return jsonify(error.messages), 400

# Authentication routes
@api.route('/register', methods=['POST'])
def register():
    try:
        user_data = user_schema.load(request.json)
    except ValidationError as err:
        return jsonify(err.messages), 400
    
    if User.query.filter_by(username=user_data['username']).first():
        return jsonify({'message': 'Username already exists'}), 400
    
    if User.query.filter_by(email=user_data['email']).first():
        return jsonify({'message': 'Email already exists'}), 400
    
    user = User(username=user_data['username'], email=user_data['email'])
    user.set_password(user_data['password'])
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({'message': 'User created successfully'}), 201

@api.route('/login', methods=['POST'])
def login():
    if not request.is_json:
        return jsonify({'message': 'Missing JSON in request'}), 400
    
    username = request.json.get('username', None)
    password = request.json.get('password', None)
    
    if not username or not password:
        return jsonify({'message': 'Missing username or password'}), 400
    
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.check_password(password):
        return jsonify({'message': 'Invalid username or password'}), 401
    
    access_token = create_access_token(identity=user.id)
    return jsonify(access_token=access_token), 200

# Book routes
@api.route('/books', methods=['GET'])
def get_books():
    books = Book.query.all()
    return jsonify(books_schema.dump(books)), 200

@api.route('/books/<int:id>', methods=['GET'])
def get_book(id):
    book = Book.query.get_or_404(id)
    return jsonify(book_schema.dump(book)), 200

@api.route('/books', methods=['POST'])
@jwt_required()
def create_book():
    try:
        book_data = book_schema.load(request.json)
    except ValidationError as err:
        return jsonify(err.messages), 400
    
    current_user_id = get_jwt_identity()
    book = Book(
        title=book_data['title'],
        description=book_data.get('description', ''),
        pages=book_data.get('pages'),
        user_id=current_user_id
    )
    
    db.session.add(book)
    db.session.commit()
    
    return jsonify(book_schema.dump(book)), 201

@api.route('/books/<int:id>', methods=['PUT'])
@jwt_required()
def update_book(id):
    book = Book.query.get_or_404(id)
    current_user_id = get_jwt_identity()
    
    if book.user_id != current_user_id:
        return jsonify({'message': 'Permission denied'}), 403
    
    try:
        book_data = book_schema.load(request.json)
    except ValidationError as err:
        return jsonify(err.messages), 400
    
    book.title = book_data['title']
    book.description = book_data.get('description', book.description)
    if 'pages' in book_data:
        book.pages = book_data['pages']
    
    db.session.commit()
    
    return jsonify(book_schema.dump(book)), 200

@api.route('/books/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_book(id):
    book = Book.query.get_or_404(id)
    current_user_id = get_jwt_identity()
    
    if book.user_id != current_user_id:
        return jsonify({'message': 'Permission denied'}), 403
    
    db.session.delete(book)
    db.session.commit()
    
    return jsonify({'message': 'Book deleted successfully'}), 200

# User routes
@api.route('/users/me', methods=['GET'])
@jwt_required()
def get_current_user():
    current_user_id = get_jwt_identity()
    user = User.query.get_or_404(current_user_id)
    return jsonify(user_schema.dump(user)), 200

@api.route('/users/me/books', methods=['GET'])
@jwt_required()
def get_user_books():
    current_user_id = get_jwt_identity()
    user = User.query.get_or_404(current_user_id)
    return jsonify(books_schema.dump(user.books)), 200
```

## Step 6: Running the Application

Now that we have set up our Flask application with models, schemas, and routes, let's run it:

```bash
python run.py
```

Your API should now be running at `http://127.0.0.1:5000/`.

## Step 7: Testing the API

Let's test our API using curl or a tool like Postman:

### Register a new user

```bash
curl -X POST http://127.0.0.1:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>", "password": "password123"}'
```

### Login and get a token

```bash
curl -X POST http://127.0.0.1:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password123"}'
```

This will return an access token that you'll need for authenticated requests.

### Create a new book

```bash
curl -X POST http://127.0.0.1:5000/api/books \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{"title": "Flask API Development", "description": "A guide to building APIs with Flask", "pages": 250}'
```

### Get all books

```bash
curl -X GET http://127.0.0.1:5000/api/books
```

### Get a specific book

```bash
curl -X GET http://127.0.0.1:5000/api/books/1
```

### Update a book

```bash
curl -X PUT http://127.0.0.1:5000/api/books/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{"title": "Advanced Flask API Development", "pages": 300}'
```

### Delete a book

```bash
curl -X DELETE http://127.0.0.1:5000/api/books/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Step 8: Adding Error Handling

To make our API more robust, let's add proper error handling. Update `app/__init__.py` to include error handlers:

```python
def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    
    # Import and register blueprints
    from app.routes import api
    app.register_blueprint(api, url_prefix='/api')
    
    # Register error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({'message': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'message': 'Internal server error'}), 500
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app
```

## Step 9: Adding Pagination

As your API grows, you'll need to implement pagination to handle large datasets efficiently. Update the `get_books` route in `app/routes.py`:

```python
@api.route('/books', methods=['GET'])
def get_books():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    # Limit per_page to prevent performance issues
    per_page = min(per_page, 100)
    
    pagination = Book.query.paginate(page=page, per_page=per_page, error_out=False)
    books = pagination.items
    
    return jsonify({
        'books': books_schema.dump(books),
        'meta': {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
    }), 200
```

## Step 10: Adding Filtering and Sorting

Let's enhance our API by adding filtering and sorting capabilities:

```python
@api.route('/books', methods=['GET'])
def get_books():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    per_page = min(per_page, 100)
    
    # Filtering
    query = Book.query
    
    title_filter = request.args.get('title')
    if title_filter:
        query = query.filter(Book.title.ilike(f'%{title_filter}%'))
    
    min_pages = request.args.get('min_pages', type=int)
    if min_pages:
        query = query.filter(Book.pages >= min_pages)
    
    max_pages = request.args.get('max_pages', type=int)
    if max_pages:
        query = query.filter(Book.pages <= max_pages)
    
    # Sorting
    sort_by = request.args.get('sort_by', 'created_at')
    if sort_by not in ['title', 'pages', 'created_at']:
        sort_by = 'created_at'
    
    sort_order = request.args.get('sort_order', 'desc')
    if sort_order == 'asc':
        query = query.order_by(getattr(Book, sort_by).asc())
    else:
        query = query.order_by(getattr(Book, sort_by).desc())
    
    # Pagination
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    books = pagination.items
    
    return jsonify({
        'books': books_schema.dump(books),
        'meta': {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
    }), 200
```

## Step 11: Adding Rate Limiting

To protect your API from abuse, you can implement rate limiting. First, install the Flask-Limiter package:

```bash
pip install flask-limiter
```

Then update `app/__init__.py`:

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# Initialize limiter
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    limiter.init_app(app)
    
    # Rest of the function remains the same
    # ...
```

Now you can apply rate limits to specific routes in `app/routes.py`:

```python
from app import limiter

@api.route('/login', methods=['POST'])
@limiter.limit("5 per minute")
def login():
    # Function remains the same
    # ...
```

## Step 12: Adding API Documentation

Good documentation is essential for any API. Let's add Swagger documentation using Flask-RESTX:

```bash
pip install flask-restx
```

Create a new file `app/api.py`:

```python
from flask_restx import Api, Resource, fields
from flask import Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models import User, Book
from app.schemas import user_schema, book_schema, books_schema
from app import db

# Create Blueprint
api_bp = Blueprint('api', __name__)
api = Api(api_bp, version='1.0', title='Book API',
          description='A simple Book API')

# Define namespaces
ns_auth = api.namespace('auth', description='Authentication operations')
ns_books = api.namespace('books', description='Book operations')
ns_users = api.namespace('users', description='User operations')

# Define models
user_model = api.model('User', {
    'username': fields.String(required=True, description='Username'),
    'email': fields.String(required=True, description='Email address'),
    'password': fields.String(required=True, description='Password')
})

login_model = api.model('Login', {
    'username': fields.String(required=True, description='Username'),
    'password': fields.String(required=True, description='Password')
})

book_model = api.model('Book', {
    'title': fields.String(required=True, description='Book title'),
    'description': fields.String(description='Book description'),
    'pages': fields.Integer(description='Number of pages')
})

# Authentication routes
@ns_auth.route('/register')
class UserRegistration(Resource):
    @api.expect(user_model)
    def post(self):
        # Implementation remains the same
        # ...

@ns_auth.route('/login')
class UserLogin(Resource):
    @api.expect(login_model)
    def post(self):
        # Implementation remains the same
        # ...

# Book routes
@ns_books.route('/')
class BookList(Resource):
    def get(self):
        # Implementation remains the same
        # ...
    
    @jwt_required()
    @api.expect(book_model)
    def post(self):
        # Implementation remains the same
        # ...

# And so on for other routes...
```

Update `app/__init__.py` to use the new API blueprint:

```python
def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    limiter.init_app(app)
    
    # Import and register blueprints
    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Rest of the function remains the same
    # ...
```

Now you can access the Swagger documentation at `http://127.0.0.1:5000/api/`.

## Step 13: Deploying Your API

Once your API is ready, you'll want to deploy it to a production environment. Here are some options:

### Option 1: Deploying to Heroku

1. Install the Heroku CLI and create an account
2. Create a `Procfile` in your project root:

```
web: gunicorn run:app
```

3. Create a `requirements.txt` file:

```bash
pip freeze > requirements.txt
```

4. Initialize a Git repository and deploy to Heroku:

```bash
git init
git add .
git commit -m "Initial commit"
heroku create your-api-name
git push heroku master
```

### Option 2: Deploying with Docker

1. Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

ENV FLASK_APP=run.py
ENV FLASK_ENV=production

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]
```

2. Create a `docker-compose.yml` file:

```yaml
version: '3'
services:
  api:
    build: .
    ports:
      - "5000:5000"
    environment:
      - SECRET_KEY=your-secret-key
      - JWT_SECRET_KEY=your-jwt-secret
      - DATABASE_URL=sqlite:///app.db
```

3. Build and run the Docker container:

```bash
docker-compose up --build
```

## Conclusion

In this tutorial, you've learned how to build a complete REST API using Flask. We've covered:

- Setting up a Flask application with proper structure
- Creating database models with SQLAlchemy
- Implementing data validation with Marshmallow
- Adding authentication with JWT
- Creating CRUD endpoints for resources
- Implementing error handling
- Adding pagination, filtering, and sorting
- Protecting your API with rate limiting
- Documenting your API with Swagger
- Deploying your API to production

This API provides a solid foundation that you can extend with additional features based on your specific requirements.

<Callout type="info">
  Remember that security is crucial for production APIs. Always use HTTPS, store secrets securely, and regularly update your dependencies to protect against vulnerabilities.
</Callout>

## Next Steps

To further enhance your API, consider exploring:

1. **Testing**: Add unit and integration tests with pytest
2. **Caching**: Implement caching with Redis to improve performance
3. **Async**: Use asynchronous Flask with ASGI servers for better concurrency
4. **Monitoring**: Add logging and monitoring with tools like Sentry
5. **Versioning**: Implement API versioning to manage changes

Happy coding!
