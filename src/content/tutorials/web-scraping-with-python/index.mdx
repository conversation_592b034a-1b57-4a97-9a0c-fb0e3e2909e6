---
title: "Web Scraping with Python: Using Beautiful Soup"
description: "Learn how to extract and analyze data from websites using Python and Beautiful Soup"
date: "2024-06-24"
lastUpdateDate: "2024-06-24"
difficulty: "intermediate"
duration: "45 minutes"
ogImage: "/tutorials/web-scraping-with-python/og-image.png"
tags:
  - python
  - web-scraping
  - beautiful-soup
  - data-extraction
---

import Callout from "@components/Callout.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";

---

## Introduction to Web Scraping

Web scraping is the process of extracting data from websites. It allows you to collect information that would be time-consuming to gather manually, such as product prices, news articles, weather data, or social media content.

In this tutorial, we'll learn how to use Python and the Beautiful Soup library to scrape web data effectively and responsibly.

<Callout type="warning">
  Always check a website's Terms of Service before scraping it. Some websites prohibit scraping, and you should respect robots.txt files. Only scrape public data and avoid overloading servers with too many requests.
</Callout>

## Prerequisites

Before starting this tutorial, you should have:

- Basic knowledge of Python (variables, loops, functions)
- Python 3.x installed on your computer
- Familiarity with HTML and CSS basics (tags, selectors)
- A code editor (VS Code, PyCharm, etc.)

If you're new to Python, check out our [Getting Started with Python tutorial](/tutorials/getting-started-with-python) first.

## Step 1: Setting Up Your Environment

First, let's set up our Python environment with the necessary libraries:

1. Open your terminal or command prompt
2. Create a new directory for your project:

```bash
mkdir web-scraping-project
cd web-scraping-project
```

3. Install the required packages:

```bash
pip install requests beautifulsoup4
```

These packages are:
- **requests**: For making HTTP requests to websites
- **beautifulsoup4**: For parsing HTML and navigating the DOM

## Step 2: Understanding the Basics

Let's start with a simple example to understand how web scraping works. We'll scrape a basic webpage to extract information.

Create a new Python file called `scraper.py` and add the following code:

```python
import requests
from bs4 import BeautifulSoup

# URL to scrape
url = "https://quotes.toscrape.com/"

# Send HTTP request to the URL
response = requests.get(url)

# Check if the request was successful
if response.status_code == 200:
    # Parse the HTML content
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # Print the title of the webpage
    print(soup.title.text)
    
    # Print the first quote
    quote = soup.find('span', class_='text')
    print(quote.text)
else:
    print(f"Failed to retrieve the webpage: Status code {response.status_code}")
```

Run this script:

```bash
python scraper.py
```

You should see the title of the webpage and the first quote displayed in your terminal.

## Step 3: Analyzing HTML Structure

Before scraping a website, you need to understand its HTML structure. Let's examine the structure of the quotes website:

1. Open your web browser and navigate to [https://quotes.toscrape.com/](https://quotes.toscrape.com/)
2. Right-click on a quote and select "Inspect" or "Inspect Element"
3. Examine the HTML structure in the developer tools panel

You'll notice that each quote is contained in a `<div class="quote">` element, which contains:
- The quote text in a `<span class="text">` element
- The author in a `<small class="author">` element
- Tags in `<a class="tag">` elements

Understanding this structure is crucial for extracting the data we want.

## Step 4: Extracting Multiple Elements

Now, let's modify our script to extract all quotes, authors, and tags from the page:

```python
import requests
from bs4 import BeautifulSoup

url = "https://quotes.toscrape.com/"
response = requests.get(url)

if response.status_code == 200:
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # Find all quote elements
    quotes = soup.find_all('div', class_='quote')
    
    for i, quote in enumerate(quotes, 1):
        # Extract the text
        text = quote.find('span', class_='text').text
        
        # Extract the author
        author = quote.find('small', class_='author').text
        
        # Extract the tags
        tags = quote.find_all('a', class_='tag')
        tag_list = [tag.text for tag in tags]
        
        # Print the information
        print(f"Quote {i}:")
        print(f"Text: {text}")
        print(f"Author: {author}")
        print(f"Tags: {', '.join(tag_list)}")
        print("-" * 50)
else:
    print(f"Failed to retrieve the webpage: Status code {response.status_code}")
```

This script will extract and print all quotes, their authors, and associated tags from the first page.

## Step 5: Navigating Multiple Pages

Many websites split their content across multiple pages. Let's modify our script to navigate through all pages of quotes:

```python
import requests
from bs4 import BeautifulSoup

base_url = "https://quotes.toscrape.com"
url = base_url
page_num = 1
all_quotes = []

while True:
    print(f"Scraping page {page_num}...")
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"Failed to retrieve page {page_num}")
        break
        
    soup = BeautifulSoup(response.text, 'html.parser')
    quotes = soup.find_all('div', class_='quote')
    
    if not quotes:
        break
        
    for quote in quotes:
        text = quote.find('span', class_='text').text
        author = quote.find('small', class_='author').text
        tags = [tag.text for tag in quote.find_all('a', class_='tag')]
        
        all_quotes.append({
            'text': text,
            'author': author,
            'tags': tags
        })
    
    # Check if there's a next page
    next_button = soup.find('li', class_='next')
    if not next_button:
        break
        
    # Get the URL of the next page
    next_page_url = next_button.find('a')['href']
    url = base_url + next_page_url
    page_num += 1

print(f"Total quotes collected: {len(all_quotes)}")

# Print the first 3 quotes as a sample
for i, quote in enumerate(all_quotes[:3], 1):
    print(f"Quote {i}:")
    print(f"Text: {quote['text']}")
    print(f"Author: {quote['author']}")
    print(f"Tags: {', '.join(quote['tags'])}")
    print("-" * 50)
```

This script will navigate through all pages of quotes and collect them in the `all_quotes` list.

<Callout type="info">
  When scraping multiple pages, it's good practice to add a delay between requests to avoid overloading the server. You can use `time.sleep(1)` to add a 1-second delay between requests.
</Callout>

## Step 6: Saving Data to a File

Now that we can collect data from multiple pages, let's save it to a CSV file for further analysis:

```python
import requests
from bs4 import BeautifulSoup
import csv
import time

base_url = "https://quotes.toscrape.com"
url = base_url
page_num = 1
all_quotes = []

while True:
    print(f"Scraping page {page_num}...")
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"Failed to retrieve page {page_num}")
        break
        
    soup = BeautifulSoup(response.text, 'html.parser')
    quotes = soup.find_all('div', class_='quote')
    
    if not quotes:
        break
        
    for quote in quotes:
        text = quote.find('span', class_='text').text
        author = quote.find('small', class_='author').text
        tags = [tag.text for tag in quote.find_all('a', class_='tag')]
        
        all_quotes.append({
            'text': text,
            'author': author,
            'tags': ','.join(tags)
        })
    
    # Check if there's a next page
    next_button = soup.find('li', class_='next')
    if not next_button:
        break
        
    # Get the URL of the next page
    next_page_url = next_button.find('a')['href']
    url = base_url + next_page_url
    page_num += 1
    
    # Add a delay to be respectful to the server
    time.sleep(1)

print(f"Total quotes collected: {len(all_quotes)}")

# Save to CSV
with open('quotes.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['text', 'author', 'tags']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for quote in all_quotes:
        writer.writerow(quote)

print("Data saved to quotes.csv")
```

This script will save all the quotes, authors, and tags to a CSV file named `quotes.csv`.

## Step 7: Handling Different Types of Websites

Different websites have different structures, and some may require additional techniques for scraping. Let's look at a few common scenarios:

### Websites with JavaScript Content

Some websites load content dynamically using JavaScript. The `requests` library can't execute JavaScript, so you might need to use a tool like Selenium:

```python
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time

# Set up Selenium
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))

# Navigate to the website
url = "https://example.com/javascript-heavy-site"
driver.get(url)

# Wait for JavaScript to load content
time.sleep(5)

# Get the page source after JavaScript execution
html = driver.page_source
soup = BeautifulSoup(html, 'html.parser')

# Now you can parse the content as usual
# ...

# Close the browser
driver.quit()
```

### Websites with Authentication

For websites that require login:

```python
import requests
from bs4 import BeautifulSoup

# Start a session
session = requests.Session()

# Login data
login_data = {
    'username': 'your_username',
    'password': 'your_password'
}

# Login URL
login_url = 'https://example.com/login'

# Post the login data
response = session.post(login_url, data=login_data)

# Now you can access protected pages using the same session
protected_url = 'https://example.com/protected-page'
response = session.get(protected_url)

# Parse the content
soup = BeautifulSoup(response.text, 'html.parser')
# ...
```

<Callout type="warning">
  Never hardcode your actual username and password in your scripts. Use environment variables or a configuration file that's not committed to version control.
</Callout>

## Step 8: Advanced Beautiful Soup Techniques

Beautiful Soup offers many powerful features for navigating and searching HTML. Here are some advanced techniques:

### CSS Selectors

You can use CSS selectors to find elements:

```python
# Find all links with the class 'external'
external_links = soup.select('a.external')

# Find all paragraphs inside a div with id 'content'
content_paragraphs = soup.select('div#content p')

# Find all elements with data attributes
data_elements = soup.select('[data-test]')
```

### Navigating the DOM

You can navigate the DOM tree:

```python
# Get the parent of an element
parent = element.parent

# Get all siblings
siblings = element.find_next_siblings()

# Get the next sibling
next_sibling = element.find_next_sibling()

# Get all children
children = element.find_all(recursive=False)
```

### Extracting Attributes

You can extract attributes from elements:

```python
# Get the href attribute of a link
link = soup.find('a')
href = link['href']

# Get all attributes as a dictionary
attrs = link.attrs
```

## Step 9: Building a Complete Web Scraper

Let's put everything together to build a more complete web scraper that:
1. Scrapes book information from [https://books.toscrape.com/](https://books.toscrape.com/)
2. Navigates through all pages
3. Extracts title, price, rating, and availability
4. Saves the data to a CSV file

```python
import requests
from bs4 import BeautifulSoup
import csv
import time

def get_rating_value(rating_class):
    ratings = {
        'One': 1,
        'Two': 2,
        'Three': 3,
        'Four': 4,
        'Five': 5
    }
    for key in ratings.keys():
        if key in rating_class:
            return ratings[key]
    return 0

def scrape_books():
    base_url = "https://books.toscrape.com/catalogue/"
    url = "https://books.toscrape.com/catalogue/page-1.html"
    page_num = 1
    all_books = []
    
    while True:
        print(f"Scraping page {page_num}...")
        response = requests.get(url)
        
        if response.status_code != 200:
            print(f"Failed to retrieve page {page_num}")
            break
            
        soup = BeautifulSoup(response.text, 'html.parser')
        books = soup.select('article.product_pod')
        
        if not books:
            break
            
        for book in books:
            # Extract title
            title = book.h3.a['title']
            
            # Extract price
            price = book.select_one('div.product_price p.price_color').text
            price = float(price.replace('£', ''))
            
            # Extract rating
            rating_class = book.select_one('p.star-rating')['class'][1]
            rating = get_rating_value(rating_class)
            
            # Extract availability
            availability = book.select_one('div.product_price p.availability').text.strip()
            
            all_books.append({
                'title': title,
                'price': price,
                'rating': rating,
                'availability': availability
            })
        
        # Check if there's a next page
        next_button = soup.select_one('li.next')
        if not next_button:
            break
            
        # Get the URL of the next page
        next_page_url = next_button.a['href']
        
        # Handle relative URLs
        if 'catalogue/' not in next_page_url:
            url = base_url + next_page_url
        else:
            url = "https://books.toscrape.com/" + next_page_url
            
        page_num += 1
        
        # Add a delay to be respectful to the server
        time.sleep(1)
    
    return all_books

def save_to_csv(books, filename='books.csv'):
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['title', 'price', 'rating', 'availability']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for book in books:
            writer.writerow(book)
    
    print(f"Data saved to {filename}")

if __name__ == "__main__":
    books = scrape_books()
    print(f"Total books collected: {len(books)}")
    save_to_csv(books)
    
    # Print a sample of the data
    for i, book in enumerate(books[:5], 1):
        print(f"Book {i}:")
        print(f"Title: {book['title']}")
        print(f"Price: £{book['price']}")
        print(f"Rating: {book['rating']} stars")
        print(f"Availability: {book['availability']}")
        print("-" * 50)
```

This script will scrape information about all books from the website and save it to a CSV file.

## Step 10: Data Analysis with Pandas

Once you've collected data through web scraping, you can analyze it using pandas:

```python
import pandas as pd
import matplotlib.pyplot as plt

# Load the data
df = pd.read_csv('books.csv')

# Display basic information
print(df.info())
print(df.describe())

# Find the most expensive book
most_expensive = df.loc[df['price'].idxmax()]
print(f"Most expensive book: {most_expensive['title']} (£{most_expensive['price']})")

# Find the highest-rated books
highest_rated = df[df['rating'] == 5]
print(f"Number of 5-star books: {len(highest_rated)}")

# Analyze price distribution by rating
rating_groups = df.groupby('rating')
avg_prices = rating_groups['price'].mean()

# Plot the results
plt.figure(figsize=(10, 6))
avg_prices.plot(kind='bar', color='skyblue')
plt.title('Average Book Price by Rating')
plt.xlabel('Rating (stars)')
plt.ylabel('Average Price (£)')
plt.xticks(rotation=0)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.savefig('price_by_rating.png')
plt.show()
```

This code will analyze the book data and create a visualization of average prices by rating.

## Conclusion

In this tutorial, you've learned:

- How to set up a Python environment for web scraping
- How to use Beautiful Soup to parse HTML and extract data
- How to navigate multiple pages and handle different website structures
- How to save scraped data to CSV files
- How to analyze the data using pandas

Web scraping is a powerful skill that can help you collect data for research, analysis, monitoring, or building datasets for machine learning. Remember to always scrape responsibly and respect website terms of service and robots.txt files.

<Callout type="info">
  For more advanced web scraping, consider exploring frameworks like Scrapy, which provides a complete solution for crawling websites and extracting structured data.
</Callout>

## Next Steps

To continue your web scraping journey:

1. Try scraping different types of websites
2. Learn about handling CAPTCHAs and other anti-scraping measures
3. Explore asynchronous scraping for better performance
4. Build a scheduled scraper that runs periodically to track changes

Happy scraping!
