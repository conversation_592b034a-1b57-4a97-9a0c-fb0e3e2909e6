---
title: "CipherVault"
description: "A secure command-line password vault built in Rust with military-grade encryption"
date: "2025-01-15"
lastUpdateDate: "2025-01-15"
tags:
  - rust
  - security
  - cryptography
  - cli
  - open-source
---

import Callout from "@components/Callout.astro";

## Project Overview

CipherVault is a secure command-line password manager built in Rust, designed with military-grade encryption and zero-knowledge architecture. It provides a fast, secure, and cross-platform solution for managing passwords, files, and sensitive data.

## Key Features

- **AES-256-GCM Encryption** with Argon2 key derivation
- **Zero-Knowledge Architecture** - Complete control over your data
- **Cross-Platform Support** - Works on Linux, macOS, and Windows
- **Secure Clipboard Integration** with auto-clear timeout
- **File Storage Capability** for any file type
- **Categories and Favorites** for organization
- **CSV Import/Export** for data migration
- **Memory Safety** guaranteed by Rust

## Technical Stack

- **Language**: Rust
- **Encryption**: AES-256-GCM with authenticated encryption
- **Key Derivation**: Argon2 (memory-hard function)
- **CLI Framework**: Clap for argument parsing
- **Serialization**: Serde for data structures
- **Security**: Zeroize for secure memory wiping

## Security Features

<Callout type="info">
**Security First**: CipherVault implements multiple layers of security to protect your sensitive data.
</Callout>

### Encryption Stack
- **AES-256-GCM**: 256-bit key strength with authenticated encryption
- **Argon2**: Memory-hard key derivation resistant to GPU attacks
- **Secure Random Generation**: OS-level entropy for cryptographic operations

### Memory Safety
- **No Buffer Overflows**: Rust's bounds checking prevents memory corruption
- **No Use-After-Free**: Ownership system prevents dangling pointers
- **Secure Memory Wiping**: Sensitive data is zeroed before deallocation

### Platform Security
- **Secure File Permissions**: Vault files are readable only by the owner
- **Auto-Clearing Clipboard**: Passwords are automatically cleared after 30 seconds
- **No Plaintext Storage**: Everything is encrypted at rest

## Usage Examples

### Basic Operations
```bash
# Initialize a new vault
ciphervault init

# Add a password entry
ciphervault add-password --title "GitHub" --username "valonmulolli" --category "Work"

# List all entries
ciphervault list

# Copy password to clipboard
ciphervault copy "GitHub"
```

### Advanced Features
```bash
# Add a file (SSH keys, documents, etc.)
ciphervault add-file --file-path ~/.ssh/id_rsa --title "SSH Key"

# Search entries with fuzzy matching
ciphervault search "git"

# Filter by category
ciphervault list --category "Work"

# Export for backup
ciphervault export --output backup.csv
```

## Architecture Highlights

### Data Structure
The vault uses a hierarchical structure optimized for both security and performance:

```rust
#[derive(Serialize, Deserialize)]
pub struct Vault {
    pub entries: Vec<Entry>,
    pub categories: Vec<String>,
    pub metadata: VaultMetadata,
}
```

### Error Handling
Comprehensive error handling using Rust's `Result` type:

```rust
#[derive(Debug, thiserror::Error)]
pub enum VaultError {
    #[error("Vault not initialized")]
    NotInitialized,
    #[error("Invalid master password")]
    InvalidPassword,
    #[error("Entry not found: {0}")]
    EntryNotFound(String),
}
```

## Performance Optimizations

- **Lazy Loading**: Large vaults are loaded efficiently on demand
- **Fuzzy Search**: Fast entry searching with relevance scoring
- **Minimal Dependencies**: Lightweight binary with fast startup times
- **Memory Efficient**: Rust's zero-cost abstractions ensure optimal performance

## Development Challenges

### Cross-Platform Compatibility
Implementing secure clipboard access across different operating systems required platform-specific code while maintaining a unified API.

### Cryptographic Implementation
Ensuring correct implementation of cryptographic primitives, including proper key derivation parameters and secure random number generation.

### User Experience
Balancing security with usability, providing clear error messages and intuitive command structure for a CLI application.

## Open Source

CipherVault is fully open source under the MIT license. The code is available for security audits and community contributions.

**Repository**: [github.com/valonmulolli/ciphervault](https://github.com/valonmulolli/ciphervault)

## Installation

### From Source
```bash
git clone https://github.com/valonmulolli/ciphervault.git
cd ciphervault
cargo build --release
```

### Global Installation
```bash
cargo install --path .
```

## Why Rust for Security Software?

Rust proved to be an excellent choice for security-critical software:

- **Memory Safety**: Prevents entire classes of vulnerabilities
- **Performance**: Zero-cost abstractions with C-level performance
- **Type Safety**: Catches errors at compile time
- **Ecosystem**: Excellent cryptography libraries and tools
- **Concurrency**: Safe parallel processing without data races

## Impact

CipherVault demonstrates that building secure, professional-grade software is accessible with modern tools like Rust. It showcases:

- Deep understanding of cryptographic principles
- Practical application of memory-safe programming
- Cross-platform development expertise
- Security-first software design
- Open source contribution to the security community

The project serves as both a practical tool for password management and an educational resource for developers interested in security and Rust programming.
