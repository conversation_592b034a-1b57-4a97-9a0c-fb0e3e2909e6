---
title: "Getting started"
date: "2025-01-05"
tags:
  - guide
---

---

## Get valon

Clone the [valon repository](https://github.com/valon/valon.git).

```sh
git clone https://github.com/valon/valon.git my-valon
```

```sh
cd my-valon
```

Install dependencies:

```sh
npm i
```

Build the site:

```sh
npm run build
```

To develop the site with live reload:

```sh
npm run dev
```

To see the site in the production mode (remember to build the site with `npm run build` first after every update):

```sh
npm run preview
```

## Customize the website metadata

To change the website metadata, edit `src/consts.ts`.

Please check the type definitions to see which values are required.

### Image paths

All image paths are relative to the `public` directory.

It's essential that you start the path with `/`. Otherwise, you might encounter issues, especially when hosting your site with `base` path set in the `astro.config.mjs` file.

### Global metadata

Start with `GLOBAL` variable:

```ts
// src/consts.ts

export const GLOBAL: GlobalSite = {
  title: "valon",
  description: "a minimalistic blog+portfolio Astro theme",
  author: "John Doe",
  authorPhotoSrc: "/johndoe.png",
  logo: {
    darkThemeSrc: "/logo/logo_dark.png",
    lightThemeSrc: "/logo/logo_light.png",
  },
};
```

- `title` and `description` - part of the metadata (head, title, etc.) and RSS.
- `author` - part of the metadata, footer and home site.
- `authorPhotoSrc` - rendered on the home site and the bio under the blog posts.
- `logo` - optional, but if present, you must provide light and dark versions. Rendered in the header. If not present, `title` text will be rendered instead.

---

### Home site

```ts
// src/consts.ts

export const HOME: HomeSite = {
  blogEntries: 5,
  projectEntries: 3,
  talkEntries: 3,
};
```

- `blogEntries`, `projectEntries`, `talkEntries` - how many entries from the particular collections are displayed on the home site.

---

### Collections site

By default, there are three collections in valon: blog, projects, and talks.

```ts
export const BLOG: BlogSite = {
  pageSize: 10,
  license: {
    name: "CC BY-NC-ND 4.0",
    href: "https://creativecommons.org/licenses/by-nc-nd/4.0",
  },
};

export const PROJECTS: CollectionSite = {
  pageSize: 10,
};

export const TALKS: CollectionSite = {
  pageSize: 10,
};
```

- `pageSize` - how many entries are displayed on the single page on the collection site.
- `license` - license information, added to all blog post sites.

There is also a special collection for tags that is fully autogenerated based on all others. You can set its name and `pageSize` in the same file:

```ts
export const TAGS: CollectionSite = {
  pageSize: 10,
};
```

---

## Add your contact details

```ts
// src/consts.ts

export const CONTACT: ContactSite = [
  {
    type: "Email",
    href: "mailto:<EMAIL>",
    displayAs: "<EMAIL>",
  },
  {
    type: "X",
    href: "https://x.com/BillGates",
    displayAs: "@BillGates on X",
  },
  {
    type: "GitHub",
    href: "https://github.com/dotnet",
  },
  {
    type: "LinkedIn",
    href: "https://www.linkedin.com/in/williamhgates/",
  },
];
```

All above are displayed below the bio.

- `type` and `href` - the type of the contact (will be rendered as the link) and the URL that the link navigates to.
- `displayAs` - optional, but if present - rendered instead of the `type`.

## Set up the address

Navigate to the `astro.config.mjs` file and set the `site` and `base` properties.

- `site` - the URL of your site, starting with protocol
- `base` - the base path of your site, if it's hosted in a subdirectory. It should start and end with a slash.

```js
// astro.config.mjs

export default defineConfig({
  site: "https://valon.github.io",
  base: "/",
  integrations: [tailwind(), sitemap(), mdx(), pagefind()],
  markdown: {
    shikiConfig: {
      theme: "css-variables",
    },
  },
});
```

## Deploy the site

valon already contains a github workflow that will deploy the page to Github Pages: `.github/workflows/deploy.yml`. The workflow is taken from the (official guide)[https://docs.astro.build/en/guides/deploy/github/].

For manual deployment instructions and other deployment options, see [Astro's docs](https://docs.astro.build/en/guides/deploy/).

## Set up Giscus

Follow the steps at [giscus.app](https://giscus.app). Once you get your custom Giscus script from that site, go to `Giscus.astro` and replace that script with your own.

```js
// src/components/Giscus.astro

<script
  src="https://giscus.app/client.js"
  data-repo="valon/valon"
  data-repo-id="R_kgDONiccDA"
  data-category="Announcements"
  data-category-id="DIC_kwDONiccDM4Cl5CN"
  data-mapping="pathname"
  data-strict="1"
  data-reactions-enabled="1"
  data-emit-metadata="0"
  data-input-position="top"
  data-theme="preferred_color_scheme"
  data-lang="en"
  data-loading="lazy"
  crossorigin="anonymous"
  async
></script>
```

To change the Giscus themes used, edit the `setGiscusTheme` function in `Head.astro`.

## RSS feed

RSS is generated automatically. The feed is available at `/feed.xml` and configured in `src/pages/feed.xml.js`.

## Create your content

### Collections

The collections are stored in the `src/content` directory and each collection has its own subdirectory.
At the beginning there are bunch of example entries (e.g. this very file) that you can safely delete, so `blog`, `projects` and `talks` directories are empty.

More: [Collections guide](../collections-guide)

### Pages

Pages are in `src/pages` directory. By default valon contains `resume` and `contact`.

### Content

valon uses markdown syntax (with MDX support) for the content.

More: [Content and syntax guide](../content-and-syntax-guide)

## Extending valon

valon is based on [Astro Micro](https://github.com/trevortylerlee/astro-micro) and [Astro Nano](https://github.com/markhorn-dev/astro-nano).

The changes are described in this post: [Everything new in valon](../everything-new-in-valon).
