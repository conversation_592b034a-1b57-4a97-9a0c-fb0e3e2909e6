---
title: "Content and syntax guide"
date: "2025-01-01"
tags:
  - guide
---

import Callout from "@components/Callout.astro";

---

## Headings

To create headings, use hash symbols (#) followed by a space. The number of hash symbols indicates the heading level.

<Callout>
  Use `h2` tags instead of `h1` tags in the post. Too many `h1` tags on a single
  page can impact SEO. The post title serves as the `h1`.
</Callout>

```md
# Heading 1

## Heading 2

### Heading 3

#### Heading 4

##### Heading 5

###### Heading 6
```

<h1>Heading 1</h1>
<h2>Heading 2</h2>
<h3>Heading 3</h3>
<h4>Heading 4</h4>
<h5>Heading 5</h5>
<h6>Heading 6</h6>

---

## Paragraphs

To create paragraphs, use a blank line to separate one or more lines of text.

{/* prettier-ignore */}
```md
<!-- empty line -->
I love Star Wars.
<!-- empty line -->
My favourite is Episode III – Revenge of the Sith.
<!-- empty line -->
```

I love Star Wars.

My favourite is Episode III – Revenge of the Sith.

---

## Italic

Use one asterisk \(\*\) or underscore \(\_\) to italicize text.

{/* prettier-ignore */}
```md
I *love* Star Wars.
My _favourite_ is Episode III – Revenge of the Sith.
```

I _love_ Star Wars.
My _favourite_ is Episode III – Revenge of the Sith.

---

## Bold

Use two asterisks \(\*\) or underscores \(\_\) to bold text.

{/* prettier-ignore */}
```md
I **love** Star Wars.
My __favourite__ is Episode III – Revenge of the Sith.
```

I **love** Star Wars.
My **favourite** is Episode III – Revenge of the Sith.

---

## Italic and Bold

Use three asterisks \(\*\) or underscores \(\_\) to both bold and italicize text.

{/* prettier-ignore */}
```md
I ***love*** Star Wars.
My ___favourite___ is Episode III – Revenge of the Sith.
```

I **_love_** Star Wars.
My **_favourite_** is Episode III – Revenge of the Sith.

---

## Horizontal Rule

Use three hyphens \(\-\), asterisks \(\*\), or underscores \(\_\) to create a horizontal rule.

{/* prettier-ignore */}
```md
<!-- empty line -->
---
<!-- empty line -->
***
<!-- empty line -->
___
<!-- empty line -->
```

---

---

---

## Links

To create a link, the link text in brackets \(\[\]\) and then follow it immediately with the URL in parentheses \(\(\)\).

```md
valon is a fork of [astro-nano](https://github.com/markhorn-dev/astro-nano).
```

valon is a fork of [astro-nano](https://github.com/markhorn-dev/astro-nano).

### Relative links

If you want to link to another page on your site, use a relative link.

```md
[Link to another post](../everything-new-in-valon)
```

[Link to another post](../everything-new-in-valon)

<Callout type="warning">
It might not work locally (in both dev and preview mdoes) and I have no idea why, I'm a purely backend engineer. The localhost hosts seem to treat the base path differenlty or something. It works fine in production, though.
And yes, I know `<base>` tag, but setting it breaks other things e.g.: Table of Contents.
</Callout>

<Callout type="info">
And yes, I know `<base>` tag, but setting it breaks other things e.g.: Table of Contents. So the solution doesn't use it.
</Callout>

### Links to public files

If you want to link to a file in the public folder, you might experience problems while hosting the site with a base path ( `base` property in the `astro.config.mjs` file).

Relative paths work fine with link (they don't with images, [see why](#public-images)):

```md
[Link to public file using relative path](../hire-me/valon_resume.pdf)
```

[Link to public file using relative path](../hire-me/valon_resume.pdf)

You might also use the dedicated component `@components/MdxPublicLink.astro` to link to public files.
Start your path with `/` and the final path will be resolved taking your `base` property into account.

```jsx
import MdxPublicLink from "@components/MdxPublicLink.astro";

<MdxPublicLink href="/hire-me/valon_resume.pdf">
  Link to public file using a dedicated Astro component
</MdxPublicLink>;
```

import MdxPublicLink from "@components/MdxPublicLink.astro";

<MdxPublicLink href="/hire-me/valon_resume.pdf">
  Link to public file using a dedicated Astro component
</MdxPublicLink>

---

## Ordered Lists

To create an ordered list, add line items with numbers followed by periods. Use an indent to create a nested list.

```md
1. Item 1
2. Item 2
   1. Sub item 1
   2. Sub item 2
3. Item 3
```

1. Item 1
2. Item 2
   1. Sub item 1
   2. Sub item 2
3. Item 3

---

## Unordered List

To create an unordered list, add a hyphen \(\-\), an asterisk \(\*\), or a plus sign \(\+\) in front of line items. Don't mix. Use an indent to create a nested list.

```md
- Item 1
- Item 2
  - Sub item 1
  - Sub item 2
- Item 3
```

- Item 1
- Item 2
  - Sub item 1
  - Sub item 2
- Item 3

---

## Images

To add an image, add an exclamation mark (!), followed by alt text in brackets ([]), and the path or URL to the image asset in parentheses.

```md
![altText](pathToImage)
```

### Relative images

Use the `![altText](./image.*)` pattern relative to the same folder as the markdown file. Notice the period.

```md
![X-Wing Starfighter](./x-wing.jpeg)
```

![X-Wing Starfighter](./x-wing.jpeg)

### Public images

Use the `![altText](/image.*)` pattern relative to the public folder. No period.

```md
![Y-Wing Starfighter](/y-wing.jpeg)
```

![Y-Wing Starfighter](/y-wing.jpeg)

<Callout type="warning">
  This doesn't work if the website is hosted with a base path (in
  `astro.config.mjs`).
</Callout>

If the website is hosted with a base path, import `@components/MdxPublicImage.astro` shared component and provide path relative to the public folder (starting with slash).

```jsx
import MdxPublicImage from "@components/MdxPublicImage.astro";

<MdxPublicImage src="/y-wing.jpeg" alt="Y-Wing Starfighter" />;
```

import MdxPublicImage from "@components/MdxPublicImage.astro";

<MdxPublicImage src="/y-wing.jpeg" alt="Y-Wing Starfighter" />

### External Image

Use the `![altText](url)` pattern.

```md
![Naboo N-1 Starfighter](https://raw.githubusercontent.com/trevortylerlee/n1/main/n1.jpeg)
```

![Naboo N-1 Starfighter](https://raw.githubusercontent.com/trevortylerlee/n1/main/n1.jpeg)

---

## Blockquotes

To add a blockquote add the greater-than character \(\>\) before a paragraph. For multi-line blockquotes, add additional greater-than character for each line and include an empty spacer line.

```md
> So this is how liberty dies...
>
> with thunderous applause.
```

> So this is how liberty dies...
>
> with thunderous applause.

---

## Strikethrough

Use a tilde \(\~\) symbol to strikethrough text.

```md
~I don't like sand.~ It's coarse and rough and irritating.
```

~I don't like sand.~ It's coarse and rough and irritating.

---

## Subscript

Use the `<sub>` tag to denote subscript.

```md
H<sub>2</sub>O
```

H<sub>2</sub>O

---

## Superscript

Use the `<sup>` tag to denote superscript.

```md
E=mc<sup>2</sup>
```

E=mc<sup>2</sup>

---

## Keyboard

Use the `<kbd>` tag to denote keys on the keyboard.

```md
<kbd>CTRL</kbd> + <kbd>ALT</kbd> + <kbd>Delete</kbd>
```

<kbd>CTRL</kbd> + <kbd>ALT</kbd> + <kbd>Delete</kbd>

---

## Abbreviate

Use the `<abbr>` tag to denote abbreviation.

```md
<abbr title="Graphics Interchange Format">GIF</abbr>
```

<abbr title="Graphics Interchange Format">GIF</abbr>

---

### Highlight

Use the `<mark>` tag to denote highlighted text.

```md
Do or do not. <mark>There is no try.</mark>
```

Do or do not. <mark>There is no try.</mark>

---

## Task Lists

Combine a list with square brackets ([]) representing a checkbox. Typing `x` inside the brackets marks the task as complete.

```md
- [x] Build a lightsaber
- [ ] Pass the Jedi Trials
- [ ] Train a padawan
```

- [x] Build a lightsaber
- [ ] Pass the Jedi Trials
- [ ] Train a padawan

---

## Tables

Use three or more hyphens (-) for the column headers and use pipes (|) to separate each column. You can align text in the columns to the left, right, or center by adding a colon (:) to the left, right, or on both side of the hyphens.

```md
| Item   | Count |
| :----- | ----: |
| X-Wing |     1 |
| Y-Wing |     2 |
| A-Wing |     3 |
```

| Item   | Count |
| :----- | ----: |
| X-Wing |     1 |
| Y-Wing |     2 |
| A-Wing |     3 |

---

## Footnotes

Add a caret (^) and an identifier inside brackets \(\[\^1\]\). Identifiers can be numbers or words but can't contain spaces or tabs.

```md
Here's a footnote, [^1] and here's another one. [^2]
[^1]: This is the first footnote.
[^2]: This is the second footnote.
```

Here's a footnote, [^1] and here's another one. [^2]
[^1]: This is the first footnote.
[^2]: This is the second footnote.

See the bottom of this page to view the footnotes.

---

## Code

To denote a word or phrase as code, enclose it in backticks (`).

```md
`package.json`
```

`package.json`

---

## Code Blocks

Denote a code block by wrapping a section of valid code in triple backticks (`). To enable syntax highlighting, type the shorthand symbol for the language after the first three backticks. [Reference for shorthand symbols](https://shiki.style/languages).

````
```js
  function hello() {
    console.log("hello world");
  }
```
````

```js
function hello() {
  console.log("hello world");
}
```

---

## Conclusion

Refer to [markdownguide.org](https://www.markdownguide.org/) for best practices as well as extended syntax.

---

## About MDX

MDX is an extension of Markdown with the ability to import `.astro`,
`.jsx`, `.tsx` and other framework components you have integrated.

This guide covers the basics of MDX syntax and how to use it, as well as a few examples.

### Importing shared components

Importing a component from the `/components` directory.

This component accepts a Javascript date object and format it as a string.

```jsx
import FormattedDate from "@components/FormattedDate.astro";

<FormattedDate date={new Date()} />;
```

import FormattedDate from "@components/FormattedDate.astro";

<FormattedDate date={new Date()} />

---

### Importing private components

Importing a component from a relative path to your content.

This component displays an alert when the button is clicked.

```jsx
import RelativeComponent from "./component.astro";

<RelativeComponent />;
```

import RelativeComponent from "./component.astro";

<RelativeComponent />

---

### Using frameworks

By default Micro has zero frameworks installed. If you install a framework, components of that framework can be used in MDX files.

<Callout>
  Don't forget to use [client
  directives](https://docs.astro.build/en/reference/directives-reference/#client-directives)
  to make framework components interactive.
</Callout>

```astro
<ReactComponent client:load />
```

---

### More Links

- [MDX Syntax Documentation](https://mdxjs.com/docs/what-is-mdx)
- [Astro Framework Integrations](https://docs.astro.build/en/guides/integrations-guide)
- [Astro Usage Documentation](https://docs.astro.build/en/guides/markdown-content/#markdown-and-mdx-pages)
- [Client Directives](https://docs.astro.build/en/reference/directives-reference/#client-directives)
