---
title: "Collections guide"
description: "Steo-by-step guide on adding a blog post or other entry"
date: "2025-01-03"
tags:
  - guide
---

import Callout from "@components/Callout.astro";

---

## Collection names

Originally, there are three collections: 

- `blog`
- `projects`
- `talks`

They work pretty much the same. If the collection is empty, the UI doesn't show it. 

For adding new collections, see the source code and follow the same pattern.

## Adding content to the collection

The collection's content is located in `src/content/COLLECTION`.

Inside, add a folder with your content in `index.mdx` (or `index.md`) file.

```
📁 /src/content/blog
└── 📁 post-1
      └── 📄 index.md
└── 📁 post-2
      └── 📄 index.mdx
```

In the above example, two blog posts will be generated with the folder name representing the slug.

- `https://example.com/blog/post-1`
- `https://example.com/blog/post-2`

---

### Provide metadata

Metadata is required for each entry.

```astro
---
title: "How to add a blog post";
description: "Steo-by-step guide on adding a blog post";
date: "2024-03-21";
lastUpdateDate: "2025-01-13";
ogImage: "/images/article-og-image.png"
draft: false;
tags:
  - guide
  - important
---
```

- `title` - will be used as header, placed in metadata and RSS.
- `description` - (optional) will be used as a subtitle, placed in metadata and in RSS. If missing, the global page description will be used in the metadata.
- `date` - publish date in format: `YYYY-MM-DD`
- `lastUpdateDate` - (optional) date of the last update in format: `YYYY-MM-DD`
- `ogImage` - (optional) path (relative to the public folder) to the open graph image for this entry 
- `draft` - (optional) if `true`, the entry won't be included into final build
- `tags` - (optional) list of tags.


<Callout type="info">
It's not required, but it's recommended to keep the title as the URL-friendly version of the post title. According to this guide, the `index.mdx` with the above content should be placed in `src/content/blog/how-to-add-a-blog-post`.
</Callout>

---

### Write content

All that's left to do is write the content under the metadata.

```astro
---
title: "How to add a blog post";
description: "Steo-by-step guide on adding a blog post";
date: "2024-03-21";
lastUpdateDate: "2025-01-13";
ogImage: "/images/article-og-image.png"
draft: false;
tags:
  - guide
  - important
---

## Before adding a blog post...
<!-- content -->
```

Content is expected to be in the Markdown format (MDX allowed in `index.mdx`).

Read more:

- [Syntax guide](../content-and-syntax-guide)

---

## Order

The order of the entries is determined by the `date` field. The newest entries will be displayed first.

However, if `lastUpdateDate` is present, the entry will be sorted by this field instead.