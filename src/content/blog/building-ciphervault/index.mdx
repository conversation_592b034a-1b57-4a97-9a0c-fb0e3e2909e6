---
title: "Building CipherVault: A Secure Password Manager in Rust"
date: "2025-01-15"
description: "How I built a military-grade password vault using Rust, AES-256-GCM encryption, and modern security practices"
tags:
  - rust
  - security
  - cryptography
  - cli
  - project
---

import Callout from "@components/Callout.astro";

When it comes to password security, I don't mess around. After years of using various password managers and being concerned about their security practices, I decided to build my own: **CipherVault** - a command-line password vault built in Rust with military-grade encryption.

## Why Build Another Password Manager?

The password manager landscape is crowded, but I had specific requirements that existing solutions didn't fully meet:

- **Zero-knowledge architecture** - I want complete control over my data
- **Military-grade encryption** - AES-256-GCM with proper key derivation
- **Command-line interface** - Fast, scriptable, and lightweight
- **Cross-platform support** - Works on Linux, macOS, and Windows
- **Memory safety** - Rust's guarantees against buffer overflows and memory leaks
- **Open source** - Full transparency in security implementation

## Technical Architecture

### Encryption Stack

CipherVault uses a robust encryption stack designed for maximum security:

```rust
// Core encryption: AES-256-GCM
use aes_gcm::{Aes256Gcm, Key, Nonce};

// Key derivation: Argon2
use argon2::{Argon2, PasswordHash, PasswordHasher};

// Secure random generation
use rand::{rngs::OsRng, RngCore};
```

**AES-256-GCM** provides:

- 256-bit key strength
- Authenticated encryption (prevents tampering)
- Galois/Counter Mode for performance

**Argon2** key derivation offers:

- Memory-hard function (resistant to GPU attacks)
- Configurable time and memory costs
- Winner of the Password Hashing Competition

### Data Structure

The vault uses a hierarchical structure for organization:

```rust
#[derive(Serialize, Deserialize)]
pub struct Vault {
    pub entries: Vec<Entry>,
    pub categories: Vec<String>,
    pub metadata: VaultMetadata,
}

#[derive(Serialize, Deserialize)]
pub struct Entry {
    pub id: String,
    pub title: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub url: Option<String>,
    pub notes: Option<String>,
    pub category: Option<String>,
    pub is_favorite: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

## Key Features Implementation

### 1. Secure Clipboard Integration

One of the most critical features is secure clipboard handling:

```rust
use clipboard::{ClipboardContext, ClipboardProvider};
use std::time::Duration;
use tokio::time::sleep;

pub async fn copy_to_clipboard_with_timeout(text: &str, timeout_secs: u64) {
    let mut ctx: ClipboardContext = ClipboardProvider::new().unwrap();
    ctx.set_contents(text.to_owned()).unwrap();

    println!("✓ Copied to clipboard (auto-clear in {}s)", timeout_secs);

    // Auto-clear after timeout
    sleep(Duration::from_secs(timeout_secs)).await;
    ctx.set_contents(String::new()).unwrap();
    println!("✓ Clipboard cleared for security");
}
```

### 2. File Storage Capability

CipherVault can store any file type securely:

```rust
pub fn add_file_entry(&mut self, file_path: &Path, title: &str, category: Option<String>) -> Result<()> {
    let file_data = fs::read(file_path)?;
    let encoded_data = base64::encode(&file_data);

    let entry = Entry {
        id: generate_id(),
        title: title.to_string(),
        file_data: Some(encoded_data),
        file_name: Some(file_path.file_name().unwrap().to_string_lossy().to_string()),
        category,
        entry_type: EntryType::File,
        ..Default::default()
    };

    self.entries.push(entry);
    Ok(())
}
```

### 3. Professional CLI Experience

Using the `clap` crate for argument parsing and custom throbber animations:

```rust
use clap::{Parser, Subcommand};

#[derive(Parser)]
#[command(name = "ciphervault")]
#[command(about = "A secure password vault built in Rust")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    Init,
    AddPassword {
        #[arg(long)]
        title: String,
        #[arg(long)]
        username: Option<String>,
        #[arg(long)]
        category: Option<String>,
    },
    List {
        #[arg(long)]
        category: Option<String>,
        #[arg(long)]
        favorites: bool,
    },
    // ... more commands
}
```

## Security Considerations

### Memory Safety

Rust's ownership system prevents common security vulnerabilities:

- **No buffer overflows** - Bounds checking at compile time
- **No use-after-free** - Ownership prevents dangling pointers
- **No data races** - Thread safety guaranteed by the type system

### Secure Memory Wiping

Sensitive data is securely wiped from memory:

```rust
use zeroize::Zeroize;

#[derive(Zeroize)]
pub struct SecretData {
    password: String,
    key: Vec<u8>,
}

impl Drop for SecretData {
    fn drop(&mut self) {
        self.zeroize();
    }
}
```

### Key Derivation Parameters

Argon2 parameters are tuned for security vs. performance:

```rust
const ARGON2_TIME_COST: u32 = 3;      // 3 iterations
const ARGON2_MEMORY_COST: u32 = 65536; // 64 MB
const ARGON2_PARALLELISM: u32 = 4;     // 4 threads
const SALT_LENGTH: usize = 32;         // 256-bit salt
```

## Development Challenges

### 1. Cross-Platform Clipboard

Different operating systems handle clipboard access differently. I had to implement platform-specific code:

```rust
#[cfg(target_os = "linux")]
use clipboard::x11_clipboard::X11ClipboardContext;

#[cfg(target_os = "windows")]
use clipboard::windows_clipboard::WindowsClipboardContext;

#[cfg(target_os = "macos")]
use clipboard::osx_clipboard::OSXClipboardContext;
```

### 2. Secure File Permissions

Ensuring the vault file has proper permissions across platforms:

```rust
#[cfg(unix)]
use std::os::unix::fs::PermissionsExt;

pub fn set_secure_permissions(path: &Path) -> Result<()> {
    #[cfg(unix)]
    {
        let mut perms = fs::metadata(path)?.permissions();
        perms.set_mode(0o600); // Read/write for owner only
        fs::set_permissions(path, perms)?;
    }
    Ok(())
}
```

### 3. Error Handling

Rust's `Result` type makes error handling explicit and safe:

```rust
#[derive(Debug, thiserror::Error)]
pub enum VaultError {
    #[error("Vault not initialized")]
    NotInitialized,
    #[error("Invalid master password")]
    InvalidPassword,
    #[error("Entry not found: {0}")]
    EntryNotFound(String),
    #[error("Encryption error: {0}")]
    EncryptionError(String),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}
```

## Performance Optimizations

### 1. Lazy Loading

Large vaults are loaded efficiently:

```rust
pub struct Vault {
    entries: Vec<Entry>,
    loaded: bool,
}

impl Vault {
    pub fn load_if_needed(&mut self) -> Result<()> {
        if !self.loaded {
            self.entries = self.load_entries_from_disk()?;
            self.loaded = true;
        }
        Ok(())
    }
}
```

### 2. Efficient Search

Search uses fuzzy matching for better UX:

```rust
use fuzzy_matcher::{FuzzyMatcher, skim::SkimMatcherV2};

pub fn search_entries(&self, query: &str) -> Vec<&Entry> {
    let matcher = SkimMatcherV2::default();

    self.entries
        .iter()
        .filter_map(|entry| {
            matcher.fuzzy_match(&entry.title, query)
                .map(|score| (entry, score))
        })
        .sorted_by(|a, b| b.1.cmp(&a.1))
        .map(|(entry, _)| entry)
        .collect()
}
```

## Usage Examples

### Basic Operations

```bash
# Initialize a new vault
ciphervault init

# Add a password
ciphervault add-password --title "GitHub" --username "valonmulolli" --category "Work"

# List all entries
ciphervault list

# Copy password to clipboard
ciphervault copy "GitHub"

# Search entries
ciphervault search "git"
```

### Advanced Features

```bash
# Add a file (documents, keys, etc.)
ciphervault add-file --file-path ~/.ssh/id_rsa --title "SSH Key"

# Export for backup
ciphervault export --output backup.csv

# Import from another password manager
ciphervault import --input lastpass_export.csv

# Filter by category
ciphervault list --category "Work"

# Show only favorites
ciphervault list --favorites
```

## Lessons Learned

### 1. Security is Hard

Implementing cryptography correctly requires deep understanding of:

- Key derivation functions
- Authenticated encryption modes
- Side-channel attack prevention
- Secure memory handling

### 2. Rust's Benefits for Security

Rust proved excellent for security-critical software:

- Memory safety prevents entire classes of vulnerabilities
- Strong type system catches errors at compile time
- Zero-cost abstractions don't sacrifice performance
- Excellent cryptography ecosystem

### 3. CLI UX Matters

Even command-line tools need good user experience:

- Clear error messages
- Helpful command suggestions
- Visual feedback (throbbers, colors)
- Intuitive command structure

## Open Source and Security

CipherVault is fully open source under the MIT license. Security through obscurity is not security at all - the code is available for audit by security researchers and the community.

The project demonstrates that with modern tools like Rust and well-established cryptographic libraries, building secure software is more accessible than ever.

## Conclusion

Building CipherVault taught me that security software requires:

- **Paranoid attention to detail** - Every decision affects security
- **Understanding of cryptographic primitives** - Not just using them correctly
- **Comprehensive testing** - Security bugs are the worst kind
- **Clear documentation** - Users need to understand security implications

If you're interested in security, cryptography, or Rust development, I encourage you to check out the [CipherVault repository](https://github.com/valonmulolli/ciphervault) and contribute to making password security more accessible.

Remember: the best password manager is the one you actually use. Whether it's CipherVault or another solution, the important thing is using unique, strong passwords for every account.

---

_Want to try CipherVault? Check out the [installation guide](https://github.com/valonmulolli/ciphervault#installation) and let me know what you think!_
